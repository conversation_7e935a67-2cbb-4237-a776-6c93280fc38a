module.exports = {

"[project]/.next-internal/server/app/api/error-analysis/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/openai.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
;
const openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
    apiKey: process.env.OPENAI_API_KEY || '********************************************************************************************************************************************************************',
    dangerouslyAllowBrowser: true
});
const __TURBOPACK__default__export__ = openai;
}),
"[project]/src/lib/utils.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "analyzeCodeContext": ()=>analyzeCodeContext,
    "cn": ()=>cn,
    "extractLineFromProgram": ()=>extractLineFromProgram,
    "formatDate": ()=>formatDate,
    "parseErrorFile": ()=>parseErrorFile
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-route] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function formatDate(date) {
    return new Intl.DateTimeFormat('fr-FR', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    }).format(date);
}
function parseErrorFile(content) {
    const lines = content.split('\n').filter((line)=>line.trim());
    return lines.map((line, index)=>{
        // Pattern principal pour les logs CAOFORS
        const caoMatch = line.match(/^(\d{2}\.\d{2}\.\d{4} \d{2}:\d{2}:\d{2})\s+(\w+)\s+(.+)$/);
        if (caoMatch) {
            const [, timestamp, level, message] = caoMatch;
            const taskMatch = message.match(/task \[([^\]]+)\]/);
            // Patterns étendus pour extraire les numéros de ligne
            const linePatterns = [
                /(?:line|ligne)[:\s]+(\d+)/i,
                /\[.*(?:line|ligne)[:\s]+(\d+)/i,
                /caofors\.ec line: (\d+)/,
                /at line (\d+)/i,
                /error on line (\d+)/i,
                /(\d+):\d+:/,
                /line (\d+) column \d+/i,
                /\((\d+),\d+\)/,
                /:\s*(\d+)\s*:/,
                /ligne\s+(\d+)/i,
                /row\s+(\d+)/i,
                /position\s+(\d+)/i // position 123
            ];
            let lineNumber;
            for (const pattern of linePatterns){
                const match = message.match(pattern);
                if (match) {
                    lineNumber = parseInt(match[1]);
                    break;
                }
            }
            // Extraction du nom de fichier
            const filePatterns = [
                /([a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z]+)/,
                /in file ([^\s]+)/i,
                /file "([^"]+)"/i,
                /([^\s]+\.(?:c|cpp|h|hpp|ec|sql|js|ts|py|java|cs))/i // common extensions
            ];
            let fileName;
            for (const pattern of filePatterns){
                const match = message.match(pattern);
                if (match) {
                    fileName = match[1];
                    break;
                }
            }
            // Détection du type d'erreur
            const errorType = detectErrorType(message);
            // Détection de la sévérité
            const severity = detectSeverity(level, message);
            return {
                timestamp,
                level,
                message,
                task: taskMatch ? taskMatch[1] : undefined,
                lineNumber,
                errorType,
                severity,
                fileName
            };
        }
        // Patterns pour d'autres formats de logs
        const genericPatterns = [
            // Format standard: ERROR: message at line 123
            /^(ERROR|WARNING|INFO|DEBUG):\s*(.+?)(?:\s+at\s+line\s+(\d+))?$/i,
            // Format avec timestamp ISO
            /^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})\s+(\w+)\s+(.+)$/,
            // Format simple: level message
            /^(\w+):\s*(.+)$/
        ];
        for (const pattern of genericPatterns){
            const match = line.match(pattern);
            if (match) {
                const level = match[1] || match[2] || 'UNKNOWN';
                const message = match[2] || match[3] || match[1] || line;
                const lineNumber = match[3] ? parseInt(match[3]) : undefined;
                return {
                    timestamp: match[1]?.includes('T') ? match[1] : '',
                    level,
                    message,
                    lineNumber,
                    errorType: detectErrorType(message),
                    severity: detectSeverity(level, message)
                };
            }
        }
        // Fallback pour les lignes non reconnues
        return {
            timestamp: '',
            level: 'UNKNOWN',
            message: line,
            lineNumber: index + 1,
            errorType: 'UNKNOWN',
            severity: 'LOW'
        };
    });
}
function detectErrorType(message) {
    const errorPatterns = [
        {
            pattern: /variable.*not.*(?:initialized|declared|defined)/i,
            type: 'VARIABLE_NOT_INITIALIZED'
        },
        {
            pattern: /undefined.*variable/i,
            type: 'UNDEFINED_VARIABLE'
        },
        {
            pattern: /syntax.*error/i,
            type: 'SYNTAX_ERROR'
        },
        {
            pattern: /compilation.*error/i,
            type: 'COMPILATION_ERROR'
        },
        {
            pattern: /runtime.*error/i,
            type: 'RUNTIME_ERROR'
        },
        {
            pattern: /null.*pointer/i,
            type: 'NULL_POINTER'
        },
        {
            pattern: /memory.*leak/i,
            type: 'MEMORY_LEAK'
        },
        {
            pattern: /buffer.*overflow/i,
            type: 'BUFFER_OVERFLOW'
        },
        {
            pattern: /division.*by.*zero/i,
            type: 'DIVISION_BY_ZERO'
        },
        {
            pattern: /file.*not.*found/i,
            type: 'FILE_NOT_FOUND'
        },
        {
            pattern: /permission.*denied/i,
            type: 'PERMISSION_DENIED'
        },
        {
            pattern: /connection.*failed/i,
            type: 'CONNECTION_ERROR'
        },
        {
            pattern: /timeout/i,
            type: 'TIMEOUT_ERROR'
        },
        {
            pattern: /locked.*already.*runs/i,
            type: 'RESOURCE_LOCKED'
        },
        {
            pattern: /sql.*error/i,
            type: 'SQL_ERROR'
        },
        {
            pattern: /database.*error/i,
            type: 'DATABASE_ERROR'
        },
        {
            pattern: /assertion.*failed/i,
            type: 'ASSERTION_FAILED'
        },
        {
            pattern: /stack.*overflow/i,
            type: 'STACK_OVERFLOW'
        },
        {
            pattern: /out.*of.*memory/i,
            type: 'OUT_OF_MEMORY'
        },
        {
            pattern: /invalid.*argument/i,
            type: 'INVALID_ARGUMENT'
        },
        {
            pattern: /type.*mismatch/i,
            type: 'TYPE_MISMATCH'
        }
    ];
    for (const { pattern, type } of errorPatterns){
        if (pattern.test(message)) {
            return type;
        }
    }
    return 'GENERAL_ERROR';
}
function detectSeverity(level, message) {
    // Sévérité basée sur le niveau
    const levelSeverity = {
        'DEBUG': 'LOW',
        'INFO': 'LOW',
        'WARNING': 'MEDIUM',
        'WARN': 'MEDIUM',
        'ERROR': 'HIGH',
        'FATAL': 'CRITICAL',
        'CRITICAL': 'CRITICAL'
    };
    let severity = levelSeverity[level.toUpperCase()] || 'MEDIUM';
    // Ajustement basé sur le contenu du message
    const criticalPatterns = [
        /crash/i,
        /fatal/i,
        /critical/i,
        /system.*failure/i,
        /memory.*corruption/i,
        /security.*breach/i
    ];
    const highPatterns = [
        /error/i,
        /exception/i,
        /failed/i,
        /abort/i,
        /null.*pointer/i,
        /buffer.*overflow/i,
        /stack.*overflow/i
    ];
    const lowPatterns = [
        /warning/i,
        /info/i,
        /debug/i,
        /notice/i
    ];
    if (criticalPatterns.some((pattern)=>pattern.test(message))) {
        severity = 'CRITICAL';
    } else if (highPatterns.some((pattern)=>pattern.test(message))) {
        severity = severity === 'LOW' ? 'HIGH' : severity;
    } else if (lowPatterns.some((pattern)=>pattern.test(message))) {
        severity = severity === 'HIGH' ? 'MEDIUM' : severity;
    }
    return severity;
}
function extractLineFromProgram(programContent, lineNumber, context = 2) {
    const lines = programContent.split('\n');
    const targetLine = lines[lineNumber - 1] || '';
    const startLine = Math.max(0, lineNumber - context - 1);
    const endLine = Math.min(lines.length, lineNumber + context);
    const contextLines = [];
    for(let i = startLine; i < endLine; i++){
        contextLines.push({
            number: i + 1,
            content: lines[i] || '',
            isTarget: i === lineNumber - 1
        });
    }
    // Analyse contextuelle avancée
    const analysis = analyzeCodeContext(lines, lineNumber, context);
    return {
        targetLine,
        contextLines,
        analysis
    };
}
function analyzeCodeContext(lines, lineNumber, context = 5) {
    const startLine = Math.max(0, lineNumber - context - 1);
    const endLine = Math.min(lines.length, lineNumber + context);
    const contextCode = lines.slice(startLine, endLine).join('\n');
    const targetLine = lines[lineNumber - 1] || '';
    // Extraction des variables
    const variables = extractVariables(contextCode);
    // Extraction des fonctions
    const functions = extractFunctions(contextCode);
    // Détection des problèmes potentiels
    const potentialIssues = detectPotentialIssues(targetLine, contextCode);
    // Génération de suggestions
    const suggestions = generateSuggestions(targetLine, contextCode, potentialIssues);
    return {
        variables,
        functions,
        potentialIssues,
        suggestions
    };
}
function extractVariables(code) {
    const variables = new Set();
    // Patterns pour différents langages
    const patterns = [
        // C/C++: type var = value; ou type var;
        /(?:int|float|double|char|long|short|unsigned|signed|bool|void)\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,
        // Variables avec déclaration explicite
        /(?:var|let|const)\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,
        // Assignations
        /([a-zA-Z_][a-zA-Z0-9_]*)\s*=/g,
        // Paramètres de fonction
        /\(\s*([a-zA-Z_][a-zA-Z0-9_]*(?:\s*,\s*[a-zA-Z_][a-zA-Z0-9_]*)*)\s*\)/g
    ];
    patterns.forEach((pattern)=>{
        let match;
        while((match = pattern.exec(code)) !== null){
            if (match[1]) {
                // Séparer les variables multiples (pour les paramètres)
                match[1].split(',').forEach((v)=>{
                    const varName = v.trim().split(/\s+/).pop();
                    if (varName && varName.length > 1) {
                        variables.add(varName);
                    }
                });
            }
        }
    });
    return Array.from(variables);
}
function extractFunctions(code) {
    const functions = new Set();
    const patterns = [
        // Définitions de fonction C/C++
        /(?:int|float|double|char|long|short|unsigned|signed|bool|void)\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g,
        // Appels de fonction
        /([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g,
        // Fonctions JavaScript/TypeScript
        /function\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,
        // Méthodes
        /\.([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g
    ];
    patterns.forEach((pattern)=>{
        let match;
        while((match = pattern.exec(code)) !== null){
            if (match[1] && match[1].length > 1) {
                functions.add(match[1]);
            }
        }
    });
    return Array.from(functions);
}
function detectPotentialIssues(targetLine, contextCode) {
    const issues = [];
    // Vérifications communes
    const checks = [
        {
            pattern: /([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*[^;]*$/,
            message: "Variable potentiellement non initialisée ou assignation incomplète"
        },
        {
            pattern: /\*\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*(?![=])/,
            message: "Déréférencement de pointeur - vérifier si le pointeur est NULL"
        },
        {
            pattern: /\[\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\]/,
            message: "Accès tableau - vérifier les limites d'index"
        },
        {
            pattern: /\/\*.*\*\//,
            message: "Code commenté - peut indiquer du code problématique"
        },
        {
            pattern: /TODO|FIXME|HACK|BUG/i,
            message: "Commentaire indiquant un problème connu"
        },
        {
            pattern: /malloc|calloc|free/,
            message: "Gestion mémoire manuelle - vérifier les fuites mémoire"
        },
        {
            pattern: /strcpy|strcat|sprintf/,
            message: "Fonction potentiellement dangereuse - risque de buffer overflow"
        }
    ];
    checks.forEach((check)=>{
        if (check.pattern.test(targetLine)) {
            issues.push(check.message);
        }
    });
    // Vérifications contextuelles
    if (contextCode.includes('EXEC SQL') && !contextCode.includes('SQLCODE_ERROR')) {
        issues.push("Requête SQL sans vérification d'erreur appropriée");
    }
    if (targetLine.includes('=') && !targetLine.includes(';')) {
        issues.push("Assignation sans point-virgule terminal");
    }
    return issues;
}
function generateSuggestions(targetLine, contextCode, issues) {
    const suggestions = [];
    // Suggestions basées sur les problèmes détectés
    if (issues.some((issue)=>issue.includes('non initialisée'))) {
        suggestions.push("Initialiser la variable avant utilisation");
        suggestions.push("Vérifier la déclaration de la variable");
    }
    if (issues.some((issue)=>issue.includes('pointeur'))) {
        suggestions.push("Ajouter une vérification NULL avant déréférencement");
        suggestions.push("Utiliser des pointeurs intelligents si possible");
    }
    if (issues.some((issue)=>issue.includes('tableau'))) {
        suggestions.push("Vérifier que l'index est dans les limites du tableau");
        suggestions.push("Utiliser des fonctions sécurisées pour l'accès aux tableaux");
    }
    if (targetLine.includes('EXEC SQL')) {
        suggestions.push("Ajouter SQLCODE_ERROR_PUTERRDB_RET après la requête SQL");
        suggestions.push("Vérifier le code de retour SQL");
    }
    // Suggestions générales
    if (targetLine.trim().length === 0) {
        suggestions.push("Ligne vide - vérifier si du code manque");
    }
    if (targetLine.includes('//') || targetLine.includes('/*')) {
        suggestions.push("Code commenté - vérifier si c'est intentionnel");
    }
    return suggestions;
}
}),
"[project]/src/agents/errorAnalysisAgent.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ErrorAnalysisService": ()=>ErrorAnalysisService,
    "errorAnalysisAgent": ()=>errorAnalysisAgent
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/openai.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-route] (ecmascript)");
;
;
const errorAnalysisAgent = {
    id: 'error-analysis-agent',
    name: 'Agent d\'Analyse d\'Erreurs',
    description: 'Analyse les fichiers de programme et d\'erreur pour détecter les erreurs, leurs emplacements et proposer des solutions',
    role: 'Expert en analyse d\'erreurs et débogage de programmes',
    goal: 'Identifier, localiser et proposer des solutions pour les erreurs dans les programmes',
    prompt: `Tu es un expert en analyse d'erreurs et débogage de programmes avec des capacités avancées d'analyse statique.
  Ton rôle est d'analyser les fichiers de programme et les fichiers d'erreur pour :
  1. Détecter et identifier les erreurs avec précision
  2. Localiser exactement où elles se produisent (ligne, colonne si possible)
  3. Analyser le contexte du code autour des erreurs
  4. Expliquer les causes possibles avec des détails techniques
  5. Proposer des solutions concrètes et détaillées
  6. Suggérer des corrections automatiques quand c'est possible
  7. Identifier les erreurs liées ou en cascade
  8. Proposer des améliorations préventives

  Utilise l'analyse contextuelle fournie (variables, fonctions, problèmes potentiels) pour enrichir tes diagnostics.
  Sois très précis dans tes analyses et fournis des solutions pratiques et applicables.
  Priorise les erreurs par ordre de sévérité et d'impact.`,
    tools: [
        'file-analysis',
        'error-parsing',
        'solution-generation'
    ],
    utils: [
        'parseErrorFile',
        'formatDate'
    ]
};
class ErrorAnalysisService {
    agent;
    constructor(){
        this.agent = errorAnalysisAgent;
    }
    async analyzeFiles(programFile, errorFile) {
        try {
            // Parse error file to extract structured error information
            const parsedErrors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parseErrorFile"])(errorFile.content);
            // Analyse statique préliminaire du code
            const staticAnalysis = this.performStaticAnalysis(programFile.content);
            // Limit program file content to avoid token limit but keep relevant sections
            let programContent = this.optimizeContentForAnalysis(programFile.content, parsedErrors);
            // Enrichir les erreurs parsées avec l'analyse contextuelle
            const enrichedErrors = this.enrichErrorsWithContext(parsedErrors, programFile.content);
            const prompt = `${this.agent.prompt}

ANALYSE STATIQUE DU PROGRAMME:
${JSON.stringify(staticAnalysis, null, 2)}

FICHIER PROGRAMME:
Nom: ${programFile.name}
Contenu (optimisé pour l'analyse):
${programContent}

FICHIER D'ERREUR:
Nom: ${errorFile.name}
Contenu:
${errorFile.content}

ERREURS PARSÉES AVEC CONTEXTE:
${JSON.stringify(enrichedErrors, null, 2)}

Analyse ces fichiers et fournis une réponse JSON structurée avec:
{
  "summary": "Résumé général de l'analyse avec priorités",
  "errors": [
    {
      "errorType": "Type d'erreur spécifique",
      "location": "Emplacement précis avec contexte",
      "description": "Description détaillée avec analyse technique",
      "possibleCauses": ["cause1 avec explication", "cause2 avec explication"],
      "solutions": ["solution1 détaillée", "solution2 avec étapes"],
      "severity": "LOW|MEDIUM|HIGH|CRITICAL",
      "lineNumber": 123,
      "fileName": "nom_du_fichier.ext",
      "autoFixSuggestions": [
        {
          "type": "REPLACE|INSERT|DELETE|WRAP",
          "description": "Description de la correction",
          "suggestedCode": "Code suggéré",
          "confidence": "LOW|MEDIUM|HIGH"
        }
      ],
      "relatedErrors": [1, 2]
    }
  ],
  "recommendations": [
    "recommandation1 avec justification",
    "recommandation2 préventive"
  ],
  "preventiveMeasures": [
    "mesure1 pour éviter les erreurs similaires",
    "mesure2 d'amélioration du code"
  ]
}

INSTRUCTIONS SPÉCIALES:
- Utilise l'analyse contextuelle fournie pour enrichir tes diagnostics
- Priorise les erreurs par sévérité et impact
- Propose des corrections automatiques quand c'est possible
- Identifie les erreurs liées ou en cascade
- Inclus des mesures préventives
- Sois très précis sur les numéros de ligne et les emplacements`;
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].chat.completions.create({
                model: "gpt-4",
                messages: [
                    {
                        role: "system",
                        content: this.agent.prompt
                    },
                    {
                        role: "user",
                        content: prompt
                    }
                ],
                temperature: 0.3,
                max_tokens: 2000
            });
            const content = response.choices[0]?.message?.content;
            if (!content) {
                throw new Error('Aucune réponse reçue de l\'API OpenAI');
            }
            // Try to parse JSON response
            let analysisResult;
            try {
                const jsonMatch = content.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    analysisResult = JSON.parse(jsonMatch[0]);
                } else {
                    // Fallback if no JSON found
                    analysisResult = {
                        summary: content,
                        errors: [],
                        recommendations: [],
                        timestamp: new Date().toISOString()
                    };
                }
            } catch (parseError) {
                // Fallback parsing
                analysisResult = {
                    summary: content,
                    errors: [],
                    recommendations: [],
                    timestamp: new Date().toISOString()
                };
            }
            analysisResult.timestamp = new Date().toISOString();
            // Enrichir les erreurs avec le contexte de code et les suggestions de correction
            if (analysisResult.errors) {
                analysisResult.errors = analysisResult.errors.map((error, index)=>{
                    let enrichedError = {
                        ...error
                    };
                    if (error.lineNumber && error.lineNumber > 0) {
                        // Ajouter le contexte de code
                        const codeContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["extractLineFromProgram"])(programFile.content, error.lineNumber);
                        enrichedError.codeContext = codeContext;
                        // Générer des suggestions de correction automatique
                        const parsedError = enrichedErrors.find((pe)=>pe.lineNumber === error.lineNumber);
                        if (parsedError) {
                            enrichedError.autoFixSuggestions = this.generateAutoFixSuggestions(parsedError, programFile.content);
                        }
                    }
                    return enrichedError;
                });
                // Identifier les erreurs liées
                analysisResult.errors = this.identifyRelatedErrors(analysisResult.errors);
            }
            return analysisResult;
        } catch (error) {
            console.error('Erreur lors de l\'analyse:', error);
            throw new Error(`Erreur lors de l'analyse des fichiers: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
        }
    }
    getAgentInfo() {
        return this.agent;
    }
    performStaticAnalysis(programContent) {
        const lines = programContent.split('\n');
        const totalLines = lines.length;
        // Analyse globale du code
        const functions = new Set();
        const variables = new Set();
        const potentialIssues = [];
        // Patterns pour détecter les fonctions
        const functionPatterns = [
            /(?:int|float|double|char|long|short|unsigned|signed|bool|void)\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g,
            /function\s+([a-zA-Z_][a-zA-Z0-9_]*)/g
        ];
        // Patterns pour détecter les variables
        const variablePatterns = [
            /(?:int|float|double|char|long|short|unsigned|signed|bool)\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,
            /(?:var|let|const)\s+([a-zA-Z_][a-zA-Z0-9_]*)/g
        ];
        // Analyse ligne par ligne
        lines.forEach((line, index)=>{
            // Détecter les fonctions
            functionPatterns.forEach((pattern)=>{
                let match;
                while((match = pattern.exec(line)) !== null){
                    functions.add(match[1]);
                }
            });
            // Détecter les variables
            variablePatterns.forEach((pattern)=>{
                let match;
                while((match = pattern.exec(line)) !== null){
                    variables.add(match[1]);
                }
            });
            // Détecter les problèmes potentiels
            if (line.includes('malloc') && !line.includes('free')) {
                potentialIssues.push(`Ligne ${index + 1}: Allocation mémoire sans libération visible`);
            }
            if (line.includes('strcpy') || line.includes('strcat')) {
                potentialIssues.push(`Ligne ${index + 1}: Fonction potentiellement dangereuse détectée`);
            }
            if (line.includes('TODO') || line.includes('FIXME')) {
                potentialIssues.push(`Ligne ${index + 1}: Code incomplet ou problématique`);
            }
        });
        // Calculer la complexité
        let complexity = 'LOW';
        if (totalLines > 1000 || functions.size > 50) {
            complexity = 'HIGH';
        } else if (totalLines > 500 || functions.size > 20) {
            complexity = 'MEDIUM';
        }
        return {
            totalLines,
            functions: Array.from(functions),
            variables: Array.from(variables),
            potentialIssues,
            complexity
        };
    }
    optimizeContentForAnalysis(programContent, parsedErrors) {
        const lines = programContent.split('\n');
        const totalLines = lines.length;
        // Si le contenu est petit, le garder entier
        if (programContent.length <= 8000) {
            return programContent;
        }
        // Identifier les lignes importantes basées sur les erreurs
        const importantLines = new Set();
        parsedErrors.forEach((error)=>{
            if (error.lineNumber) {
                // Ajouter la ligne d'erreur et son contexte
                for(let i = Math.max(0, error.lineNumber - 10); i <= Math.min(totalLines - 1, error.lineNumber + 10); i++){
                    importantLines.add(i);
                }
            }
        });
        // Ajouter le début et la fin du fichier
        for(let i = 0; i < Math.min(50, totalLines); i++){
            importantLines.add(i);
        }
        for(let i = Math.max(0, totalLines - 50); i < totalLines; i++){
            importantLines.add(i);
        }
        // Construire le contenu optimisé
        const sortedLines = Array.from(importantLines).sort((a, b)=>a - b);
        let optimizedContent = '';
        let lastLine = -1;
        sortedLines.forEach((lineIndex)=>{
            if (lineIndex > lastLine + 1) {
                optimizedContent += '\n... [LIGNES OMISES] ...\n';
            }
            optimizedContent += `${lineIndex + 1}: ${lines[lineIndex]}\n`;
            lastLine = lineIndex;
        });
        return optimizedContent;
    }
    enrichErrorsWithContext(parsedErrors, programContent) {
        return parsedErrors.map((error)=>{
            if (error.lineNumber) {
                const contextAnalysis = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["analyzeCodeContext"])(programContent.split('\n'), error.lineNumber);
                return {
                    ...error,
                    context: {
                        variables: contextAnalysis.variables,
                        functions: contextAnalysis.functions,
                        potentialIssues: contextAnalysis.potentialIssues,
                        suggestions: contextAnalysis.suggestions
                    }
                };
            }
            return error;
        });
    }
    generateAutoFixSuggestions(error, programContent) {
        const suggestions = [];
        if (!error.lineNumber) return suggestions;
        const lines = programContent.split('\n');
        const targetLine = lines[error.lineNumber - 1];
        // Suggestions basées sur le type d'erreur
        switch(error.errorType){
            case 'VARIABLE_NOT_INITIALIZED':
                if (targetLine.includes('=')) {
                    suggestions.push({
                        type: 'REPLACE',
                        description: 'Initialiser la variable à une valeur par défaut',
                        originalCode: targetLine,
                        suggestedCode: targetLine.replace(/([a-zA-Z_][a-zA-Z0-9_]*)\s*;/, '$1 = 0;'),
                        confidence: 'MEDIUM',
                        lineNumber: error.lineNumber
                    });
                }
                break;
            case 'RESOURCE_LOCKED':
                suggestions.push({
                    type: 'INSERT',
                    description: 'Ajouter une vérification de verrou avant exécution',
                    suggestedCode: 'if (!is_task_running(task_name)) {',
                    confidence: 'HIGH',
                    lineNumber: error.lineNumber
                });
                break;
            case 'SQL_ERROR':
                if (targetLine.includes('EXEC SQL') && !targetLine.includes('SQLCODE_ERROR')) {
                    suggestions.push({
                        type: 'INSERT',
                        description: 'Ajouter la vérification d\'erreur SQL',
                        suggestedCode: 'SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "description");',
                        confidence: 'HIGH',
                        lineNumber: error.lineNumber + 1
                    });
                }
                break;
        }
        return suggestions;
    }
    identifyRelatedErrors(errors) {
        return errors.map((error, index)=>{
            const relatedErrors = [];
            // Chercher des erreurs sur des lignes proches
            errors.forEach((otherError, otherIndex)=>{
                if (index !== otherIndex && error.lineNumber && otherError.lineNumber) {
                    const lineDiff = Math.abs(error.lineNumber - otherError.lineNumber);
                    if (lineDiff <= 5) {
                        relatedErrors.push(otherIndex);
                    }
                }
            });
            // Chercher des erreurs du même type
            errors.forEach((otherError, otherIndex)=>{
                if (index !== otherIndex && error.errorType === otherError.errorType) {
                    relatedErrors.push(otherIndex);
                }
            });
            return {
                ...error,
                relatedErrors: [
                    ...new Set(relatedErrors)
                ] // Supprimer les doublons
            };
        });
    }
    // Nouvelle méthode pour analyser un programme sans fichier d'erreur
    async analyzeCodeOnly(programFile) {
        try {
            // Analyse statique complète
            const staticAnalysis = this.performStaticAnalysis(programFile.content);
            const detectedIssues = this.detectStaticIssues(programFile.content);
            const prompt = `${this.agent.prompt}

ANALYSE STATIQUE SEULE (sans fichier d'erreur):

FICHIER PROGRAMME:
Nom: ${programFile.name}
Contenu:
${programFile.content.length > 8000 ? programFile.content.substring(0, 8000) + '\n\n... [CONTENU TRONQUÉ] ...' : programFile.content}

ANALYSE STATIQUE:
${JSON.stringify(staticAnalysis, null, 2)}

PROBLÈMES DÉTECTÉS:
${JSON.stringify(detectedIssues, null, 2)}

Effectue une analyse statique complète et fournis une réponse JSON avec:
{
  "summary": "Résumé de l'analyse statique avec évaluation de la qualité du code",
  "errors": [
    {
      "errorType": "Type de problème détecté",
      "location": "Emplacement dans le code",
      "description": "Description du problème potentiel",
      "possibleCauses": ["causes possibles"],
      "solutions": ["solutions recommandées"],
      "severity": "LOW|MEDIUM|HIGH|CRITICAL",
      "lineNumber": 123,
      "autoFixSuggestions": [...]
    }
  ],
  "recommendations": ["améliorations générales du code"],
  "codeQuality": {
    "score": 85,
    "strengths": ["points forts"],
    "weaknesses": ["points faibles"],
    "maintainability": "LOW|MEDIUM|HIGH"
  }
}`;
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].chat.completions.create({
                model: "gpt-4",
                messages: [
                    {
                        role: "system",
                        content: this.agent.prompt
                    },
                    {
                        role: "user",
                        content: prompt
                    }
                ],
                temperature: 0.2,
                max_tokens: 3000
            });
            const content = response.choices[0]?.message?.content;
            if (!content) {
                throw new Error('Aucune réponse reçue de l\'API OpenAI');
            }
            // Parse JSON response
            let analysisResult;
            try {
                const jsonMatch = content.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    analysisResult = JSON.parse(jsonMatch[0]);
                } else {
                    analysisResult = {
                        summary: content,
                        errors: [],
                        recommendations: [],
                        timestamp: new Date().toISOString()
                    };
                }
            } catch (parseError) {
                analysisResult = {
                    summary: content,
                    errors: [],
                    recommendations: [],
                    timestamp: new Date().toISOString()
                };
            }
            analysisResult.timestamp = new Date().toISOString();
            // Enrichir avec le contexte de code
            if (analysisResult.errors) {
                analysisResult.errors = analysisResult.errors.map((error)=>{
                    if (error.lineNumber && error.lineNumber > 0) {
                        const codeContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["extractLineFromProgram"])(programFile.content, error.lineNumber);
                        return {
                            ...error,
                            codeContext
                        };
                    }
                    return error;
                });
            }
            return analysisResult;
        } catch (error) {
            console.error('Erreur lors de l\'analyse statique:', error);
            throw new Error(`Erreur lors de l'analyse statique: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
        }
    }
    detectStaticIssues(programContent) {
        const issues = [];
        const lines = programContent.split('\n');
        lines.forEach((line, index)=>{
            const lineNumber = index + 1;
            // Détection de problèmes de sécurité
            if (/strcpy|strcat|sprintf|gets/.test(line)) {
                issues.push({
                    type: 'SECURITY_RISK',
                    description: 'Utilisation de fonction potentiellement dangereuse',
                    lineNumber,
                    severity: 'HIGH'
                });
            }
            // Détection de fuites mémoire potentielles
            if (/malloc|calloc/.test(line) && !programContent.includes('free')) {
                issues.push({
                    type: 'MEMORY_LEAK',
                    description: 'Allocation mémoire sans libération visible',
                    lineNumber,
                    severity: 'MEDIUM'
                });
            }
            // Détection de variables non initialisées
            if (/(?:int|float|double|char)\s+[a-zA-Z_][a-zA-Z0-9_]*\s*;/.test(line)) {
                issues.push({
                    type: 'UNINITIALIZED_VARIABLE',
                    description: 'Variable déclarée mais non initialisée',
                    lineNumber,
                    severity: 'MEDIUM'
                });
            }
            // Détection de code mort
            if (/\/\*.*\*\//.test(line) && line.includes('TODO')) {
                issues.push({
                    type: 'DEAD_CODE',
                    description: 'Code commenté ou incomplet',
                    lineNumber,
                    severity: 'LOW'
                });
            }
            // Détection de complexité excessive
            if ((line.match(/if|while|for|switch/g) || []).length > 3) {
                issues.push({
                    type: 'HIGH_COMPLEXITY',
                    description: 'Ligne avec complexité cyclomatique élevée',
                    lineNumber,
                    severity: 'LOW'
                });
            }
            // Détection de SQL sans vérification d'erreur
            if (/EXEC SQL/.test(line) && !lines[index + 1]?.includes('SQLCODE_ERROR')) {
                issues.push({
                    type: 'SQL_NO_ERROR_CHECK',
                    description: 'Requête SQL sans vérification d\'erreur',
                    lineNumber,
                    severity: 'HIGH'
                });
            }
        });
        return issues;
    }
}
}),
"[project]/src/app/api/error-analysis/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GET": ()=>GET,
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$agents$2f$errorAnalysisAgent$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/agents/errorAnalysisAgent.ts [app-route] (ecmascript)");
;
;
async function POST(request) {
    try {
        const body = await request.json();
        const { programFile, errorFile } = body;
        if (!programFile || !errorFile) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Les fichiers programme et erreur sont requis'
            }, {
                status: 400
            });
        }
        if (!programFile.content || !errorFile.content) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Le contenu des fichiers ne peut pas être vide'
            }, {
                status: 400
            });
        }
        const errorAnalysisService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$agents$2f$errorAnalysisAgent$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ErrorAnalysisService"]();
        const result = await errorAnalysisService.analyzeFiles(programFile, errorFile);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: result,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Erreur lors de l\'analyse d\'erreurs:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: error instanceof Error ? error.message : 'Erreur interne du serveur',
            timestamp: new Date().toISOString()
        }, {
            status: 500
        });
    }
}
async function GET() {
    const errorAnalysisService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$agents$2f$errorAnalysisAgent$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ErrorAnalysisService"]();
    const agentInfo = errorAnalysisService.getAgentInfo();
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        success: true,
        data: agentInfo,
        timestamp: new Date().toISOString()
    });
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__006eb312._.js.map