(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[446],{668:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>R});var s=r(5155),a=r(2115),n=r(6695),l=r(5476),i=r(8539),c=r(2523),o=r(7434),d=r(9946);let u=(0,d.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]),m=(0,d.A)("file-x",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"m14.5 12.5-5 5",key:"b62r18"}],["path",{d:"m9.5 12.5 5 5",key:"1rk7el"}]]),x=(0,d.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),p=(0,d.A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);var h=r(4213),f=r(4186);let g=(0,d.A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),b=(0,d.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]);function y(e){return Math.ceil(e.length/4)}let v={TEXT:[".txt",".log",".md",".csv"],CODE:[".c",".cpp",".ec",".js",".ts",".py",".java",".php",".html",".css",".sql"],DATA:[".json",".xml",".yaml",".yml"],OFFICE:[".doc",".docx",".xls",".xlsx",".ppt",".pptx"],PDF:[".pdf"],ERROR:[".error",".err",".trace",".dump"]};function j(){return[...v.TEXT,...v.CODE,...v.DATA,...v.OFFICE,...v.PDF,...v.ERROR]}function N(e){let t=e.lastIndexOf(".");return -1!==t?e.substring(t).toLowerCase():""}function w(e){let t=N(e);return v.TEXT.includes(t)?"text":v.CODE.includes(t)?"code":v.DATA.includes(t)?"data":v.OFFICE.includes(t)?"office":v.PDF.includes(t)?"pdf":v.ERROR.includes(t)?"error":"unknown"}async function k(e){let t=w(e.name);try{switch(t){case"text":case"code":case"data":case"error":default:return await T(e);case"office":return await C(e);case"pdf":return await A(e)}}catch(r){return{content:"",originalName:e.name,fileType:t,success:!1,error:r instanceof Error?r.message:"Erreur inconnue"}}}async function T(e){return new Promise(t=>{let r=new FileReader;r.onload=r=>{var s;t({content:null==(s=r.target)?void 0:s.result,originalName:e.name,fileType:w(e.name),success:!0})},r.onerror=()=>{t({content:"",originalName:e.name,fileType:w(e.name),success:!1,error:"Erreur lors de la lecture du fichier"})},r.readAsText(e,"UTF-8")})}async function C(e){try{let t=await e.arrayBuffer(),r=new TextDecoder("utf-8",{fatal:!1}).decode(t).replace(/[\x00-\x1F\x7F-\x9F]/g," ").trim();if(r.length<50)return{content:"",originalName:e.name,fileType:"office",success:!1,error:"Fichier Office d\xe9tect\xe9. Pour un meilleur support, convertissez en .txt ou .pdf"};return{content:r,originalName:e.name,fileType:"office",success:!0}}catch(t){return{content:"",originalName:e.name,fileType:"office",success:!1,error:"Impossible de lire le fichier Office. Essayez de le convertir en .txt"}}}async function A(e){return{content:"",originalName:e.name,fileType:"pdf",success:!1,error:"Fichiers PDF non support\xe9s actuellement. Convertissez en .txt pour l'analyse"}}function R(){let[e,t]=(0,a.useState)(""),[r,d]=(0,a.useState)("mysql"),[w,T]=(0,a.useState)(!0),[C,A]=(0,a.useState)(!1),[R,S]=(0,a.useState)(null),[E,O]=(0,a.useState)(""),[q,F]=(0,a.useState)(null),[L,D]=(0,a.useState)(""),[M,z]=(0,a.useState)(""),Z=e=>{let t=e.length,r=y(e),s=y(e)>5e3;F({length:t,estimatedTokens:r,exceedsLimit:s,willBeTruncated:s})},B=async e=>{var r;let s=null==(r=e.target.files)?void 0:r[0];if(s){if(z(""),D(""),!function(e){let t=N(e);return j().includes(t)}(s.name))return void z("Type de fichier non support\xe9: ".concat(s.name));try{let e=await k(s);e.success?(t(e.content),D(e.originalName),Z(e.content),"office"===e.fileType&&z("Fichier Office trait\xe9 avec support limit\xe9. Pour de meilleurs r\xe9sultats, convertissez en .txt")):z(e.error||"Erreur lors du traitement du fichier")}catch(e){z("Erreur lors du chargement du fichier")}}},_=async()=>{if(!e.trim())return void O("Veuillez fournir une sp\xe9cification");A(!0),O(""),S(null);try{let t=function(e){let t=e.length;y(e);let r=e,s=!1;return y(e)>5e3&&(r=function(e){let t=e.split("\n"),r=[],s=["table","column","field","primary key","foreign key","index","constraint","relationship","join","select","insert","update","delete","create","alter","drop","database","schema","view","procedure","function","trigger","unique","not null","default","auto_increment"];for(let e of t){let t=e.toLowerCase();s.some(e=>t.includes(e))?r.push(e):e.trim().length>0&&e.trim().length<100&&(e.includes(":")||e.match(/^[A-Z]/)||e.includes("="))&&r.push(e)}let a=r.join("\n");if(a.length>12e3){let e=Math.floor(.4*r.length);a=r.slice(0,e).join("\n")+"\n\n[... Sp\xe9cification tronqu\xe9e pour respecter les limites de tokens ...]"}return a.length>12e3&&(a=a.slice(0,12e3)+"\n\n[... Texte tronqu\xe9 ...]"),a||e.slice(0,12e3)+"\n\n[... Texte tronqu\xe9 ...]"}(e),s=!0),{processedText:r,wasTruncated:s,originalLength:t,processedLength:r.length,estimatedTokens:y(r)}}(e),s={specification:t.processedText,databaseType:r,includeComments:w};t.wasTruncated&&(console.log("Sp\xe9cification tronqu\xe9e: ".concat(t.originalLength," → ").concat(t.processedLength," caract\xe8res")),console.log("Tokens estim\xe9s apr\xe8s traitement: ".concat(t.estimatedTokens)));let a=await fetch("/api/sql-generation",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)}),n=await a.json();if(!a.ok)throw Error(n.error||"Erreur lors de la g\xe9n\xe9ration");S(n.data)}catch(e){O(e instanceof Error?e.message:"Une erreur est survenue lors de la g\xe9n\xe9ration")}finally{A(!1)}},P=async e=>{try{await navigator.clipboard.writeText(e)}catch(e){console.error("Failed to copy text: ",e)}};return(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-[#002857] mb-4",children:"G\xe9n\xe9ration SQL"}),(0,s.jsx)("p",{className:"text-gray-600",children:"G\xe9n\xe9rez des scripts SQL optimis\xe9s \xe0 partir de vos sp\xe9cifications fonctionnelles."})]}),(0,s.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12",children:[(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)(n.Zp,{className:"bg-white/80 backdrop-blur-xl border-0 shadow-2xl shadow-[#002857]/10 rounded-3xl overflow-hidden",children:[(0,s.jsxs)(n.aR,{className:"bg-gradient-to-r from-[#002857] to-[#003d7a] text-white p-8",children:[(0,s.jsxs)(n.ZB,{className:"flex items-center text-2xl font-bold",children:[(0,s.jsx)(o.A,{className:"w-7 h-7 mr-3"}),"Sp\xe9cification"]}),(0,s.jsx)(n.BT,{className:"text-blue-100 text-base",children:"D\xe9crivez les tables, relations et contraintes que vous souhaitez cr\xe9er"})]}),(0,s.jsxs)(n.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,s.jsx)(c.p,{type:"file",accept:j().join(","),onChange:B,className:"flex-1"}),L&&(0,s.jsxs)("span",{className:"text-sm text-green-600 flex items-center",children:[(0,s.jsx)(u,{className:"w-4 h-4 mr-1"}),L]}),M&&(0,s.jsxs)("span",{className:"text-sm text-red-600 flex items-center",children:[(0,s.jsx)(m,{className:"w-4 h-4 mr-1"}),M]})]}),(0,s.jsx)("div",{className:"text-center text-gray-500 text-sm mb-4",children:"ou"}),(0,s.jsxs)("details",{className:"mb-4",children:[(0,s.jsx)("summary",{className:"text-sm text-gray-600 cursor-pointer hover:text-gray-800",children:"\uD83D\uDCC1 Types de fichiers support\xe9s"}),(0,s.jsx)("div",{className:"mt-2 p-3 bg-gray-50 rounded text-xs text-gray-600",children:(0,s.jsx)("pre",{className:"whitespace-pre-wrap",children:"\nTypes de fichiers support\xe9s :\n• Texte : ".concat(v.TEXT.join(", "),"\n• Code : ").concat(v.CODE.join(", "),"\n• Donn\xe9es : ").concat(v.DATA.join(", "),"\n• Erreurs : ").concat(v.ERROR.join(", "),"\n• Office : ").concat(v.OFFICE.join(", ")," (support limit\xe9)\n• PDF : ").concat(v.PDF.join(", ")," (non support\xe9 actuellement)\n  ").trim()})})]})]}),(0,s.jsx)(i.T,{placeholder:"Exemple: Cr\xe9er une base de donn\xe9es pour un syst\xe8me de gestion des commandes avec les tables suivantes: - Table clients (id, nom, email, t\xe9l\xe9phone, adresse) - Table produits (id, nom, description, prix, stock) - Table commandes (id, client_id, date_commande, statut, total) - Table d\xe9tails_commande (id, commande_id, produit_id, quantit\xe9, prix_unitaire)  Contraintes: - Cl\xe9s \xe9trang\xe8res appropri\xe9es - Index sur les colonnes fr\xe9quemment recherch\xe9es - Contraintes de validation sur les prix (> 0)",value:e,onChange:e=>{var r;t(r=e.target.value),Z(r)},className:"min-h-[300px]"}),q&&(0,s.jsx)("div",{className:"mt-2 p-3 rounded-lg border ".concat(q.exceedsLimit?"bg-orange-50 border-orange-200":"bg-blue-50 border-blue-200"),children:(0,s.jsxs)("div",{className:"flex items-start gap-2",children:[q.exceedsLimit?(0,s.jsx)(x,{className:"w-5 h-5 text-orange-500 mt-0.5"}):(0,s.jsx)(p,{className:"w-5 h-5 text-blue-500 mt-0.5"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("div",{className:"text-sm font-medium",children:q.exceedsLimit?"Texte tr\xe8s long d\xe9tect\xe9":"Analyse du texte"}),(0,s.jsxs)("div",{className:"text-xs text-gray-600 mt-1",children:[(0,s.jsxs)("div",{children:["Longueur: ",q.length.toLocaleString()," caract\xe8res"]}),(0,s.jsxs)("div",{children:["Tokens estim\xe9s: ",q.estimatedTokens.toLocaleString()]}),q.willBeTruncated&&(0,s.jsx)("div",{className:"text-orange-600 font-medium mt-1",children:"⚠️ Le texte sera automatiquement r\xe9sum\xe9 pour respecter les limites de l'IA"})]})]})]})})]})]}),(0,s.jsxs)(n.Zp,{className:"bg-white/80 backdrop-blur-xl border-0 shadow-2xl shadow-[#ff7514]/10 rounded-3xl overflow-hidden",children:[(0,s.jsx)(n.aR,{className:"bg-gradient-to-r from-[#ff7514] to-[#ff8c42] text-white p-8",children:(0,s.jsxs)(n.ZB,{className:"flex items-center text-2xl font-bold",children:[(0,s.jsx)(h.A,{className:"w-7 h-7 mr-3"}),"Options de G\xe9n\xe9ration"]})}),(0,s.jsxs)(n.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Type de Base de Donn\xe9es"}),(0,s.jsxs)("select",{value:r,onChange:e=>d(e.target.value),className:"w-full p-2 border border-gray-300 rounded-md",children:[(0,s.jsx)("option",{value:"mysql",children:"MySQL"}),(0,s.jsx)("option",{value:"postgresql",children:"PostgreSQL"}),(0,s.jsx)("option",{value:"sqlite",children:"SQLite"}),(0,s.jsx)("option",{value:"sqlserver",children:"SQL Server"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{type:"checkbox",id:"includeComments",checked:w,onChange:e=>T(e.target.checked),className:"rounded"}),(0,s.jsx)("label",{htmlFor:"includeComments",className:"text-sm",children:"Inclure des commentaires explicatifs"})]})]})]}),(0,s.jsx)(l.$,{onClick:_,disabled:C||!e.trim(),className:"w-full bg-gradient-to-r from-[#002857] to-[#003d7a] hover:from-[#001a3d] hover:to-[#002857] text-white py-4 rounded-2xl shadow-xl shadow-[#002857]/25 transform hover:scale-105 transition-all duration-300",size:"lg",children:C?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(f.A,{className:"w-5 h-5 mr-2 animate-spin"}),"G\xe9n\xe9ration en cours..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(h.A,{className:"w-5 h-5 mr-2"}),"G\xe9n\xe9rer le Script SQL"]})}),E&&(0,s.jsx)("div",{className:"bg-[#002857]/5 border border-[#002857]/20 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-[#002857]",children:E})})]}),(0,s.jsxs)("div",{children:[R&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(n.Zp,{className:"bg-white/80 backdrop-blur-xl border-0 shadow-2xl shadow-[#ff7514]/10 rounded-3xl overflow-hidden",children:[(0,s.jsxs)(n.aR,{className:"bg-gradient-to-r from-[#ff7514] to-[#ff8c42] text-white p-8",children:[(0,s.jsxs)(n.ZB,{className:"flex items-center justify-between text-2xl font-bold",children:[(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)(h.A,{className:"w-7 h-7 mr-3"}),"Script SQL G\xe9n\xe9r\xe9"]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>P(R.sql),className:"bg-white/20 border-white/30 text-white hover:bg-white/30",children:[(0,s.jsx)(g,{className:"w-4 h-4 mr-1"}),"Copier"]}),(0,s.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>{if(!R)return;let e=new Blob([R.sql],{type:"text/sql"}),t=URL.createObjectURL(e),r=document.createElement("a");r.href=t,r.download="generated_script_".concat(new Date().toISOString().slice(0,10),".sql"),document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(t)},className:"bg-white/20 border-white/30 text-white hover:bg-white/30",children:[(0,s.jsx)(b,{className:"w-4 h-4 mr-1"}),"T\xe9l\xe9charger"]})]})]}),(0,s.jsxs)(n.BT,{children:["G\xe9n\xe9r\xe9 le ",new Date(R.timestamp).toLocaleString("fr-FR")]})]}),(0,s.jsx)(n.Wu,{children:(0,s.jsx)("pre",{className:"bg-gray-50 p-4 rounded-lg overflow-x-auto text-sm font-mono whitespace-pre-wrap",children:R.sql})})]}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsx)(n.ZB,{children:"Explication"})}),(0,s.jsx)(n.Wu,{children:(0,s.jsx)("p",{className:"text-gray-700 whitespace-pre-wrap",children:R.explanation})})]}),(R.tables.length>0||R.operations.length>0)&&(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsx)(n.ZB,{children:"D\xe9tails du Script"})}),(0,s.jsxs)(n.Wu,{className:"space-y-4",children:[R.tables.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("h4",{className:"font-medium mb-2",children:["Tables cr\xe9\xe9es (",R.tables.length,")"]}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:R.tables.map((e,t)=>(0,s.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm",children:e},t))})]}),R.operations.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium mb-2",children:"Op\xe9rations SQL"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:R.operations.map((e,t)=>(0,s.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-800 rounded text-sm",children:e},t))})]})]})]})]}),!R&&!C&&(0,s.jsx)(n.Zp,{children:(0,s.jsxs)(n.Wu,{className:"text-center py-12",children:[(0,s.jsx)(h.A,{className:"w-16 h-16 mx-auto text-gray-300 mb-4"}),(0,s.jsx)("p",{className:"text-gray-500",children:"Le script SQL g\xe9n\xe9r\xe9 appara\xeetra ici une fois que vous aurez fourni une sp\xe9cification et lanc\xe9 la g\xe9n\xe9ration."})]})})]})]})]})}},2523:(e,t,r)=>{"use strict";r.d(t,{p:()=>l});var s=r(5155),a=r(2115),n=r(9434);let l=a.forwardRef((e,t)=>{let{className:r,type:a,...l}=e;return(0,s.jsx)("input",{type:a,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...l})});l.displayName="Input"},4186:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4213:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},4524:(e,t,r)=>{Promise.resolve().then(r.bind(r,668))},5476:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(5155),a=r(2115),n=r(2596);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=n.$;var c=r(9434);let o=((e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:a,defaultVariants:n}=t,c=Object.keys(a).map(e=>{let t=null==r?void 0:r[e],s=null==n?void 0:n[e];if(null===t)return null;let i=l(t)||l(s);return a[e][i]}),o=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return i(e,c,null==t||null==(s=t.compoundVariants)?void 0:s.reduce((e,t)=>{let{class:r,className:s,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...n,...o}[t]):({...n,...o})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)})("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,t)=>{let{className:r,variant:a,size:n,asChild:l=!1,...i}=e;return(0,s.jsx)("button",{className:(0,c.cn)(o({variant:a,size:n,className:r})),ref:t,...i})});d.displayName="Button"},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>c,Zp:()=>l,aR:()=>i});var s=r(5155),a=r(2115),n=r(9434);let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});l.displayName="Card";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...a})});i.displayName="CardHeader";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});c.displayName="CardTitle";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...a})});o.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...a})});d.displayName="CardContent",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter"},7434:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},8539:(e,t,r)=>{"use strict";r.d(t,{T:()=>l});var s=r(5155),a=r(2115),n=r(9434);let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...a})});l.displayName="Textarea"},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(2596),a=r(9688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}}},e=>{e.O(0,[598,441,964,358],()=>e(e.s=4524)),_N_E=e.O()}]);