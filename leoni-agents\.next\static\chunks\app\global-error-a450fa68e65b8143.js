(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[219],{2880:(e,r,t)=>{Promise.resolve().then(t.bind(t,8385))},8385:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var l=t(5155),s=t(2115);function n(e){let{error:r,reset:t}=e;return(0,s.useEffect)(()=>{console.error(r)},[r]),(0,l.jsx)("html",{children:(0,l.jsx)("body",{children:(0,l.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100",children:(0,l.jsx)("div",{className:"max-w-md w-full mx-4",children:(0,l.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8 text-center",children:[(0,l.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,l.jsx)("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Erreur Critique"}),(0,l.jsx)("p",{className:"text-gray-600 mb-6",children:"Une erreur critique s'est produite. Veuillez recharger la page."}),(0,l.jsx)("button",{onClick:t,className:"w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300",children:"Recharger la page"}),!1]})})})})})}}},e=>{e.O(0,[441,964,358],()=>e(e.s=2880)),_N_E=e.O()}]);