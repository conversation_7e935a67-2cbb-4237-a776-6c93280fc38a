(()=>{var a={};a.id=446,a.ids=[446],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10022:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19346:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Leoni\\\\leoni-agents\\\\src\\\\app\\\\sql-generation\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\sql-generation\\page.tsx","default")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27540:(a,b,c)=>{Promise.resolve().then(c.bind(c,19346))},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34729:(a,b,c)=>{"use strict";c.d(b,{T:()=>g});var d=c(60687),e=c(43210),f=c(4780);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("textarea",{className:(0,f.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:c,...b}));g.displayName="Textarea"},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},44493:(a,b,c)=>{"use strict";c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h});var d=c(60687),e=c(43210),f=c(4780);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("h3",{ref:c,className:(0,f.cn)("text-2xl font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("p",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b})).displayName="CardFooter"},46100:(a,b,c)=>{Promise.resolve().then(c.bind(c,79855))},47015:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>k});var d=c(60687);c(43210);var e=c(29523),f=c(93613),g=c(78122),h=c(32192),i=c(85814),j=c.n(i);function k({error:a,reset:b}){return(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 p-4",children:(0,d.jsx)("div",{className:"max-w-md w-full",children:(0,d.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8 text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r from-[#002857] to-[#003d7a] rounded-full flex items-center justify-center mx-auto mb-6",children:(0,d.jsx)(f.A,{className:"w-8 h-8 text-white"})}),(0,d.jsx)("h1",{className:"text-2xl font-bold text-[#002857] mb-4",children:"Erreur de G\xe9n\xe9ration SQL"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:"Une erreur s'est produite lors de la g\xe9n\xe9ration SQL. Veuillez r\xe9essayer ou retourner \xe0 l'accueil."}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)(e.$,{onClick:b,className:"w-full bg-gradient-to-r from-[#002857] to-[#003d7a] hover:from-[#001a3d] hover:to-[#002857] text-white",children:[(0,d.jsx)(g.A,{className:"w-4 h-4 mr-2"}),"R\xe9essayer la g\xe9n\xe9ration"]}),(0,d.jsx)(j(),{href:"/",children:(0,d.jsxs)(e.$,{variant:"outline",className:"w-full border-[#ff7514] text-[#ff7514] hover:bg-[#ff7514] hover:text-white",children:[(0,d.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"Retour \xe0 l'accueil"]})})]})]})})})}},48730:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},52701:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Leoni\\\\leoni-agents\\\\src\\\\app\\\\sql-generation\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\sql-generation\\error.tsx","default")},56097:(a,b,c)=>{Promise.resolve().then(c.bind(c,52701))},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67539:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>B.default,__next_app__:()=>H,handler:()=>J,pages:()=>G,routeModule:()=>I,tree:()=>F});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(31369),C=c(30893),D=c(52836),E={};for(let a in C)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(E[a]=()=>C[a]);c.d(b,E);let F={children:["",{children:["sql-generation",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,19346)),"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\sql-generation\\page.tsx"]}]},{error:[()=>Promise.resolve().then(c.bind(c,52701)),"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\sql-generation\\error.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,54431)),"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(c.bind(c,67393)),"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\loading.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,31369)),"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,31369)),"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\global-error.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,G=["C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\sql-generation\\page.tsx"],H={require:c,loadChunk:()=>Promise.resolve()},I=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/sql-generation/page",pathname:"/sql-generation",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:F},distDir:".next",projectDir:""});async function J(a,b,c){var d;let E="/sql-generation/page";"/index"===E&&(E="/");let K="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await I.prepare(a,b,{srcPage:E,multiZoneDraftMode:K});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(E),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===I.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&I.isDev&&(az=_);let aA={...C,tree:F,pages:G,GlobalError:B.default,handler:J,routeModule:I,__next_app__:H};W&&X&&(0,n.setReferenceManifestsSingleton)({page:E,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return I.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:I,page:E,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:I.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:K,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>I.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:I.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!I.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===I.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await I.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await I.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),I.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!I.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&D.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await I.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},79855:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>C});var d=c(60687),e=c(43210),f=c(44493),g=c(29523),h=c(34729),i=c(89667),j=c(10022),k=c(62688);let l=(0,k.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]),m=(0,k.A)("file-x",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"m14.5 12.5-5 5",key:"b62r18"}],["path",{d:"m9.5 12.5 5 5",key:"1rk7el"}]]),n=(0,k.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),o=(0,k.A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);var p=c(61611),q=c(48730);let r=(0,k.A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),s=(0,k.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]);function t(a){return Math.ceil(a.length/4)}let u={TEXT:[".txt",".log",".md",".csv"],CODE:[".c",".cpp",".ec",".js",".ts",".py",".java",".php",".html",".css",".sql"],DATA:[".json",".xml",".yaml",".yml"],OFFICE:[".doc",".docx",".xls",".xlsx",".ppt",".pptx"],PDF:[".pdf"],ERROR:[".error",".err",".trace",".dump"]};function v(){return[...u.TEXT,...u.CODE,...u.DATA,...u.OFFICE,...u.PDF,...u.ERROR]}function w(a){let b=a.lastIndexOf(".");return -1!==b?a.substring(b).toLowerCase():""}function x(a){let b=w(a);return u.TEXT.includes(b)?"text":u.CODE.includes(b)?"code":u.DATA.includes(b)?"data":u.OFFICE.includes(b)?"office":u.PDF.includes(b)?"pdf":u.ERROR.includes(b)?"error":"unknown"}async function y(a){let b=x(a.name);try{switch(b){case"text":case"code":case"data":case"error":default:return await z(a);case"office":return await A(a);case"pdf":return await B(a)}}catch(c){return{content:"",originalName:a.name,fileType:b,success:!1,error:c instanceof Error?c.message:"Erreur inconnue"}}}async function z(a){return new Promise(b=>{let c=new FileReader;c.onload=c=>{b({content:c.target?.result,originalName:a.name,fileType:x(a.name),success:!0})},c.onerror=()=>{b({content:"",originalName:a.name,fileType:x(a.name),success:!1,error:"Erreur lors de la lecture du fichier"})},c.readAsText(a,"UTF-8")})}async function A(a){try{let b=await a.arrayBuffer(),c=new TextDecoder("utf-8",{fatal:!1}).decode(b).replace(/[\x00-\x1F\x7F-\x9F]/g," ").trim();if(c.length<50)return{content:"",originalName:a.name,fileType:"office",success:!1,error:"Fichier Office d\xe9tect\xe9. Pour un meilleur support, convertissez en .txt ou .pdf"};return{content:c,originalName:a.name,fileType:"office",success:!0}}catch(b){return{content:"",originalName:a.name,fileType:"office",success:!1,error:"Impossible de lire le fichier Office. Essayez de le convertir en .txt"}}}async function B(a){return{content:"",originalName:a.name,fileType:"pdf",success:!1,error:"Fichiers PDF non support\xe9s actuellement. Convertissez en .txt pour l'analyse"}}function C(){let[a,b]=(0,e.useState)(""),[c,k]=(0,e.useState)("mysql"),[x,z]=(0,e.useState)(!0),[A,B]=(0,e.useState)(!1),[C,D]=(0,e.useState)(null),[E,F]=(0,e.useState)(""),[G,H]=(0,e.useState)(null),[I,J]=(0,e.useState)(""),[K,L]=(0,e.useState)(""),M=a=>{let b=a.length,c=t(a),d=t(a)>5e3;H({length:b,estimatedTokens:c,exceedsLimit:d,willBeTruncated:d})},N=async a=>{let c=a.target.files?.[0];if(c){if(L(""),J(""),!function(a){let b=w(a);return v().includes(b)}(c.name))return void L(`Type de fichier non support\xe9: ${c.name}`);try{let a=await y(c);a.success?(b(a.content),J(a.originalName),M(a.content),"office"===a.fileType&&L("Fichier Office trait\xe9 avec support limit\xe9. Pour de meilleurs r\xe9sultats, convertissez en .txt")):L(a.error||"Erreur lors du traitement du fichier")}catch(a){L("Erreur lors du chargement du fichier")}}},O=async()=>{if(!a.trim())return void F("Veuillez fournir une sp\xe9cification");B(!0),F(""),D(null);try{let b=function(a){let b=a.length;t(a);let c=a,d=!1;return t(a)>5e3&&(c=function(a){let b=a.split("\n"),c=[],d=["table","column","field","primary key","foreign key","index","constraint","relationship","join","select","insert","update","delete","create","alter","drop","database","schema","view","procedure","function","trigger","unique","not null","default","auto_increment"];for(let a of b){let b=a.toLowerCase();d.some(a=>b.includes(a))?c.push(a):a.trim().length>0&&a.trim().length<100&&(a.includes(":")||a.match(/^[A-Z]/)||a.includes("="))&&c.push(a)}let e=c.join("\n");if(e.length>12e3){let a=Math.floor(.4*c.length);e=c.slice(0,a).join("\n")+"\n\n[... Sp\xe9cification tronqu\xe9e pour respecter les limites de tokens ...]"}return e.length>12e3&&(e=e.slice(0,12e3)+"\n\n[... Texte tronqu\xe9 ...]"),e||a.slice(0,12e3)+"\n\n[... Texte tronqu\xe9 ...]"}(a),d=!0),{processedText:c,wasTruncated:d,originalLength:b,processedLength:c.length,estimatedTokens:t(c)}}(a),d={specification:b.processedText,databaseType:c,includeComments:x};b.wasTruncated&&(console.log(`Sp\xe9cification tronqu\xe9e: ${b.originalLength} → ${b.processedLength} caract\xe8res`),console.log(`Tokens estim\xe9s apr\xe8s traitement: ${b.estimatedTokens}`));let e=await fetch("/api/sql-generation",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(d)}),f=await e.json();if(!e.ok)throw Error(f.error||"Erreur lors de la g\xe9n\xe9ration");D(f.data)}catch(a){F(a instanceof Error?a.message:"Une erreur est survenue lors de la g\xe9n\xe9ration")}finally{B(!1)}},P=async a=>{try{await navigator.clipboard.writeText(a)}catch(a){console.error("Failed to copy text: ",a)}};return(0,d.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,d.jsxs)("div",{className:"text-center mb-8",children:[(0,d.jsx)("h1",{className:"text-4xl font-bold text-[#002857] mb-4",children:"G\xe9n\xe9ration SQL"}),(0,d.jsx)("p",{className:"text-gray-600",children:"G\xe9n\xe9rez des scripts SQL optimis\xe9s \xe0 partir de vos sp\xe9cifications fonctionnelles."})]}),(0,d.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12",children:[(0,d.jsxs)("div",{className:"space-y-8",children:[(0,d.jsxs)(f.Zp,{className:"bg-white/80 backdrop-blur-xl border-0 shadow-2xl shadow-[#002857]/10 rounded-3xl overflow-hidden",children:[(0,d.jsxs)(f.aR,{className:"bg-gradient-to-r from-[#002857] to-[#003d7a] text-white p-8",children:[(0,d.jsxs)(f.ZB,{className:"flex items-center text-2xl font-bold",children:[(0,d.jsx)(j.A,{className:"w-7 h-7 mr-3"}),"Sp\xe9cification"]}),(0,d.jsx)(f.BT,{className:"text-blue-100 text-base",children:"D\xe9crivez les tables, relations et contraintes que vous souhaitez cr\xe9er"})]}),(0,d.jsxs)(f.Wu,{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,d.jsx)(i.p,{type:"file",accept:v().join(","),onChange:N,className:"flex-1"}),I&&(0,d.jsxs)("span",{className:"text-sm text-green-600 flex items-center",children:[(0,d.jsx)(l,{className:"w-4 h-4 mr-1"}),I]}),K&&(0,d.jsxs)("span",{className:"text-sm text-red-600 flex items-center",children:[(0,d.jsx)(m,{className:"w-4 h-4 mr-1"}),K]})]}),(0,d.jsx)("div",{className:"text-center text-gray-500 text-sm mb-4",children:"ou"}),(0,d.jsxs)("details",{className:"mb-4",children:[(0,d.jsx)("summary",{className:"text-sm text-gray-600 cursor-pointer hover:text-gray-800",children:"\uD83D\uDCC1 Types de fichiers support\xe9s"}),(0,d.jsx)("div",{className:"mt-2 p-3 bg-gray-50 rounded text-xs text-gray-600",children:(0,d.jsx)("pre",{className:"whitespace-pre-wrap",children:`
Types de fichiers support\xe9s :
• Texte : ${u.TEXT.join(", ")}
• Code : ${u.CODE.join(", ")}
• Donn\xe9es : ${u.DATA.join(", ")}
• Erreurs : ${u.ERROR.join(", ")}
• Office : ${u.OFFICE.join(", ")} (support limit\xe9)
• PDF : ${u.PDF.join(", ")} (non support\xe9 actuellement)
  `.trim()})})]})]}),(0,d.jsx)(h.T,{placeholder:"Exemple: Cr\xe9er une base de donn\xe9es pour un syst\xe8me de gestion des commandes avec les tables suivantes: - Table clients (id, nom, email, t\xe9l\xe9phone, adresse) - Table produits (id, nom, description, prix, stock) - Table commandes (id, client_id, date_commande, statut, total) - Table d\xe9tails_commande (id, commande_id, produit_id, quantit\xe9, prix_unitaire)  Contraintes: - Cl\xe9s \xe9trang\xe8res appropri\xe9es - Index sur les colonnes fr\xe9quemment recherch\xe9es - Contraintes de validation sur les prix (> 0)",value:a,onChange:a=>{var c;b(c=a.target.value),M(c)},className:"min-h-[300px]"}),G&&(0,d.jsx)("div",{className:`mt-2 p-3 rounded-lg border ${G.exceedsLimit?"bg-orange-50 border-orange-200":"bg-blue-50 border-blue-200"}`,children:(0,d.jsxs)("div",{className:"flex items-start gap-2",children:[G.exceedsLimit?(0,d.jsx)(n,{className:"w-5 h-5 text-orange-500 mt-0.5"}):(0,d.jsx)(o,{className:"w-5 h-5 text-blue-500 mt-0.5"}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("div",{className:"text-sm font-medium",children:G.exceedsLimit?"Texte tr\xe8s long d\xe9tect\xe9":"Analyse du texte"}),(0,d.jsxs)("div",{className:"text-xs text-gray-600 mt-1",children:[(0,d.jsxs)("div",{children:["Longueur: ",G.length.toLocaleString()," caract\xe8res"]}),(0,d.jsxs)("div",{children:["Tokens estim\xe9s: ",G.estimatedTokens.toLocaleString()]}),G.willBeTruncated&&(0,d.jsx)("div",{className:"text-orange-600 font-medium mt-1",children:"⚠️ Le texte sera automatiquement r\xe9sum\xe9 pour respecter les limites de l'IA"})]})]})]})})]})]}),(0,d.jsxs)(f.Zp,{className:"bg-white/80 backdrop-blur-xl border-0 shadow-2xl shadow-[#ff7514]/10 rounded-3xl overflow-hidden",children:[(0,d.jsx)(f.aR,{className:"bg-gradient-to-r from-[#ff7514] to-[#ff8c42] text-white p-8",children:(0,d.jsxs)(f.ZB,{className:"flex items-center text-2xl font-bold",children:[(0,d.jsx)(p.A,{className:"w-7 h-7 mr-3"}),"Options de G\xe9n\xe9ration"]})}),(0,d.jsxs)(f.Wu,{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Type de Base de Donn\xe9es"}),(0,d.jsxs)("select",{value:c,onChange:a=>k(a.target.value),className:"w-full p-2 border border-gray-300 rounded-md",children:[(0,d.jsx)("option",{value:"mysql",children:"MySQL"}),(0,d.jsx)("option",{value:"postgresql",children:"PostgreSQL"}),(0,d.jsx)("option",{value:"sqlite",children:"SQLite"}),(0,d.jsx)("option",{value:"sqlserver",children:"SQL Server"})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("input",{type:"checkbox",id:"includeComments",checked:x,onChange:a=>z(a.target.checked),className:"rounded"}),(0,d.jsx)("label",{htmlFor:"includeComments",className:"text-sm",children:"Inclure des commentaires explicatifs"})]})]})]}),(0,d.jsx)(g.$,{onClick:O,disabled:A||!a.trim(),className:"w-full bg-gradient-to-r from-[#002857] to-[#003d7a] hover:from-[#001a3d] hover:to-[#002857] text-white py-4 rounded-2xl shadow-xl shadow-[#002857]/25 transform hover:scale-105 transition-all duration-300",size:"lg",children:A?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(q.A,{className:"w-5 h-5 mr-2 animate-spin"}),"G\xe9n\xe9ration en cours..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(p.A,{className:"w-5 h-5 mr-2"}),"G\xe9n\xe9rer le Script SQL"]})}),E&&(0,d.jsx)("div",{className:"bg-[#002857]/5 border border-[#002857]/20 rounded-lg p-4",children:(0,d.jsx)("p",{className:"text-[#002857]",children:E})})]}),(0,d.jsxs)("div",{children:[C&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)(f.Zp,{className:"bg-white/80 backdrop-blur-xl border-0 shadow-2xl shadow-[#ff7514]/10 rounded-3xl overflow-hidden",children:[(0,d.jsxs)(f.aR,{className:"bg-gradient-to-r from-[#ff7514] to-[#ff8c42] text-white p-8",children:[(0,d.jsxs)(f.ZB,{className:"flex items-center justify-between text-2xl font-bold",children:[(0,d.jsxs)("span",{className:"flex items-center",children:[(0,d.jsx)(p.A,{className:"w-7 h-7 mr-3"}),"Script SQL G\xe9n\xe9r\xe9"]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsxs)(g.$,{variant:"outline",size:"sm",onClick:()=>P(C.sql),className:"bg-white/20 border-white/30 text-white hover:bg-white/30",children:[(0,d.jsx)(r,{className:"w-4 h-4 mr-1"}),"Copier"]}),(0,d.jsxs)(g.$,{variant:"outline",size:"sm",onClick:()=>{if(!C)return;let a=new Blob([C.sql],{type:"text/sql"}),b=URL.createObjectURL(a),c=document.createElement("a");c.href=b,c.download=`generated_script_${new Date().toISOString().slice(0,10)}.sql`,document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(b)},className:"bg-white/20 border-white/30 text-white hover:bg-white/30",children:[(0,d.jsx)(s,{className:"w-4 h-4 mr-1"}),"T\xe9l\xe9charger"]})]})]}),(0,d.jsxs)(f.BT,{children:["G\xe9n\xe9r\xe9 le ",new Date(C.timestamp).toLocaleString("fr-FR")]})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsx)("pre",{className:"bg-gray-50 p-4 rounded-lg overflow-x-auto text-sm font-mono whitespace-pre-wrap",children:C.sql})})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsx)(f.aR,{children:(0,d.jsx)(f.ZB,{children:"Explication"})}),(0,d.jsx)(f.Wu,{children:(0,d.jsx)("p",{className:"text-gray-700 whitespace-pre-wrap",children:C.explanation})})]}),(C.tables.length>0||C.operations.length>0)&&(0,d.jsxs)(f.Zp,{children:[(0,d.jsx)(f.aR,{children:(0,d.jsx)(f.ZB,{children:"D\xe9tails du Script"})}),(0,d.jsxs)(f.Wu,{className:"space-y-4",children:[C.tables.length>0&&(0,d.jsxs)("div",{children:[(0,d.jsxs)("h4",{className:"font-medium mb-2",children:["Tables cr\xe9\xe9es (",C.tables.length,")"]}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:C.tables.map((a,b)=>(0,d.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm",children:a},b))})]}),C.operations.length>0&&(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium mb-2",children:"Op\xe9rations SQL"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:C.operations.map((a,b)=>(0,d.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-800 rounded text-sm",children:a},b))})]})]})]})]}),!C&&!A&&(0,d.jsx)(f.Zp,{children:(0,d.jsxs)(f.Wu,{className:"text-center py-12",children:[(0,d.jsx)(p.A,{className:"w-16 h-16 mx-auto text-gray-300 mb-4"}),(0,d.jsx)("p",{className:"text-gray-500",children:"Le script SQL g\xe9n\xe9r\xe9 appara\xeetra ici une fois que vous aurez fourni une sp\xe9cification et lanc\xe9 la g\xe9n\xe9ration."})]})})]})]})]})}},85601:(a,b,c)=>{Promise.resolve().then(c.bind(c,47015))},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},89667:(a,b,c)=>{"use strict";c.d(b,{p:()=>g});var d=c(60687),e=c(43210),f=c(4780);let g=e.forwardRef(({className:a,type:b,...c},e)=>(0,d.jsx)("input",{type:b,className:(0,f.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:e,...c}));g.displayName="Input"}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,565,748],()=>b(b.s=67539));module.exports=c})();