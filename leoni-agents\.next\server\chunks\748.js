exports.id=748,exports.ids=[748],exports.modules={4780:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(49384),e=c(82348);function f(...a){return(0,e.QP)((0,d.$)(a))}},8811:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(60687);function e({error:a,reset:b}){return(0,d.jsx)("html",{children:(0,d.jsx)("body",{children:(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100",children:(0,d.jsx)("div",{className:"max-w-md w-full mx-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8 text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,d.jsx)("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Erreur Critique"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:"Une erreur critique s'est produite. Veuillez recharger la page."}),(0,d.jsx)("button",{onClick:b,className:"w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300",children:"Recharger la page"}),!1]})})})})})}c(43210)},14329:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>h});var d=c(60687);c(43210);var e=c(29523),f=c(93613),g=c(78122);function h({error:a,reset:b}){return(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100",children:(0,d.jsx)("div",{className:"max-w-md w-full mx-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8 text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r from-[#ff7514] to-[#ff8c42] rounded-full flex items-center justify-center mx-auto mb-6",children:(0,d.jsx)(f.A,{className:"w-8 h-8 text-white"})}),(0,d.jsx)("h1",{className:"text-2xl font-bold text-[#002857] mb-4",children:"Oups ! Une erreur s'est produite"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:"Nous rencontrons un probl\xe8me technique temporaire. Veuillez r\xe9essayer dans quelques instants."}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)(e.$,{onClick:b,className:"w-full bg-gradient-to-r from-[#ff7514] to-[#ff8c42] hover:from-[#e6650f] hover:to-[#ff7514] text-white",children:[(0,d.jsx)(g.A,{className:"w-4 h-4 mr-2"}),"R\xe9essayer"]}),(0,d.jsx)(e.$,{variant:"outline",onClick:()=>window.location.href="/",className:"w-full border-[#002857] text-[#002857] hover:bg-[#002857] hover:text-white",children:"Retour \xe0 l'accueil"})]}),!1]})})})}},22807:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f});var d=c(60687),e=c(56085);function f(){return(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r from-[#ff7514] to-[#ff8c42] rounded-full flex items-center justify-center mx-auto mb-6 animate-pulse",children:(0,d.jsx)(e.A,{className:"w-8 h-8 text-white animate-spin"})}),(0,d.jsx)("div",{className:"absolute inset-0 w-16 h-16 bg-gradient-to-r from-[#ff7514] to-[#ff8c42] rounded-full mx-auto animate-ping opacity-20"})]}),(0,d.jsx)("h2",{className:"text-2xl font-bold text-[#002857] mb-2",children:"Leoni Agents"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:"Chargement en cours..."}),(0,d.jsx)("div",{className:"flex justify-center",children:(0,d.jsxs)("div",{className:"flex space-x-1",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-[#ff7514] rounded-full animate-bounce"}),(0,d.jsx)("div",{className:"w-2 h-2 bg-[#ff7514] rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,d.jsx)("div",{className:"w-2 h-2 bg-[#ff7514] rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})})]})})}},29523:(a,b,c)=>{"use strict";c.d(b,{$:()=>i});var d=c(60687),e=c(43210),f=c(24224),g=c(4780);let h=(0,f.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),i=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...f},i)=>(0,d.jsx)("button",{className:(0,g.cn)(h({variant:b,size:c,className:a})),ref:i,...f}));i.displayName="Button"},31104:(a,b,c)=>{Promise.resolve().then(c.bind(c,57347))},31369:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Leoni\\\\leoni-agents\\\\src\\\\app\\\\global-error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\global-error.tsx","default")},46039:(a,b,c)=>{Promise.resolve().then(c.bind(c,88928))},47108:(a,b,c)=>{Promise.resolve().then(c.bind(c,22807))},53103:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},54413:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Leoni\\\\leoni-agents\\\\src\\\\app\\\\not-found.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\not-found.tsx","default")},54431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Leoni\\\\leoni-agents\\\\src\\\\app\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\error.tsx","default")},57347:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>k});var d=c(60687),e=c(85814),f=c.n(e),g=c(29523),h=c(99270),i=c(32192),j=c(28559);function k(){return(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100",children:(0,d.jsx)("div",{className:"max-w-lg w-full mx-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8 text-center",children:[(0,d.jsx)("div",{className:"w-20 h-20 bg-gradient-to-r from-[#002857] to-[#003d7a] rounded-full flex items-center justify-center mx-auto mb-6",children:(0,d.jsx)(h.A,{className:"w-10 h-10 text-white"})}),(0,d.jsx)("h1",{className:"text-6xl font-bold text-[#002857] mb-4",children:"404"}),(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Page non trouv\xe9e"}),(0,d.jsx)("p",{className:"text-gray-600 mb-8",children:"D\xe9sol\xe9, la page que vous recherchez n'existe pas ou a \xe9t\xe9 d\xe9plac\xe9e."}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)(f(),{href:"/",children:(0,d.jsxs)(g.$,{className:"w-full bg-gradient-to-r from-[#ff7514] to-[#ff8c42] hover:from-[#e6650f] hover:to-[#ff7514] text-white",children:[(0,d.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"Retour \xe0 l'accueil"]})}),(0,d.jsxs)(g.$,{variant:"outline",onClick:()=>window.history.back(),className:"w-full border-[#002857] text-[#002857] hover:bg-[#002857] hover:text-white",children:[(0,d.jsx)(j.A,{className:"w-4 h-4 mr-2"}),"Page pr\xe9c\xe9dente"]})]}),(0,d.jsx)("div",{className:"mt-8 pt-6 border-t border-gray-200",children:(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"Besoin d'aide ? Contactez notre \xe9quipe support."})})]})})})}},57464:(a,b,c)=>{Promise.resolve().then(c.bind(c,31369))},59382:(a,b,c)=>{Promise.resolve().then(c.bind(c,54431))},61135:()=>{},65668:(a,b,c)=>{Promise.resolve().then(c.bind(c,67393))},67393:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Leoni\\\\leoni-agents\\\\src\\\\app\\\\loading.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\loading.tsx","default")},67552:(a,b,c)=>{Promise.resolve().then(c.bind(c,54413))},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},82942:(a,b,c)=>{Promise.resolve().then(c.bind(c,14329))},86246:(a,b,c)=>{"use strict";c.d(b,{default:()=>n});var d=c(60687),e=c(85814),f=c.n(e),g=c(16189),h=c(4780),i=c(32192),j=c(48210),k=c(61611),l=c(56085);let m=[{name:"Accueil",href:"/",icon:i.A},{name:"Analyse d'Erreurs",href:"/error-analysis",icon:j.A},{name:"G\xe9n\xe9ration SQL",href:"/sql-generation",icon:k.A}];function n(){let a=(0,g.usePathname)();return(0,d.jsxs)("nav",{className:"relative",children:[(0,d.jsx)("div",{className:"bg-gradient-to-r from-[#002857] to-[#003d7a] shadow-xl shadow-[#002857]/20 border-b border-[#003d7a]/30",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"flex justify-between h-20 items-center",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-[#ff7514] to-[#ff8c42] rounded-xl flex items-center justify-center shadow-lg shadow-[#ff7514]/30",children:(0,d.jsx)(l.A,{className:"w-7 h-7 text-white"})}),(0,d.jsx)("div",{className:"absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-[#ff8c42] to-[#ff7514] rounded-full animate-pulse"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Leoni Agents"}),(0,d.jsx)("p",{className:"text-xs text-[#ff8c42] font-medium",children:"Intelligence Artificielle"})]})]}),(0,d.jsx)("div",{className:"hidden sm:flex sm:space-x-2",children:m.map(b=>{let c=b.icon,e=a===b.href;return(0,d.jsxs)(f(),{href:b.href,className:(0,h.cn)("group relative inline-flex items-center px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 transform hover:scale-105",e?"bg-gradient-to-r from-[#ff7514] to-[#ff8c42] text-white shadow-lg shadow-[#ff7514]/25":"text-white/80 hover:text-white hover:bg-white/10 hover:shadow-md"),"aria-current":e?"page":void 0,children:[(0,d.jsx)(c,{className:(0,h.cn)("w-5 h-5 mr-2 transition-all duration-300",e?"text-white":"text-white/70 group-hover:text-[#ff8c42]")}),b.name,e&&(0,d.jsx)("div",{className:"absolute inset-0 rounded-2xl bg-gradient-to-r from-[#ff7514] to-[#ff8c42] opacity-20 animate-pulse"})]},b.name)})})]})})}),(0,d.jsx)("div",{className:"sm:hidden px-4 pt-2 pb-3 space-y-1",children:m.map(b=>{let c=b.icon,e=a===b.href;return(0,d.jsx)(f(),{href:b.href,className:(0,h.cn)("block pl-3 pr-4 py-2 border-l-4 text-base font-medium",e?"bg-blue-50 border-blue-500 text-blue-700":"border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700"),"aria-current":e?"page":void 0,children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(c,{className:"w-4 h-4 mr-3"}),b.name]})},b.name)})})]})}},88928:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Leoni\\\\leoni-agents\\\\src\\\\components\\\\Navigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\components\\Navigation.tsx","default")},92312:(a,b,c)=>{Promise.resolve().then(c.bind(c,8811))},94431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>k,metadata:()=>j});var d=c(37413),e=c(22376),f=c.n(e),g=c(68726),h=c.n(g);c(61135);var i=c(88928);let j={title:"Leoni Agents - Syst\xe8me d'Agents Intelligents",description:"Syst\xe8me d'agents pour l'analyse d'erreurs et la g\xe9n\xe9ration SQL"};function k({children:a}){return(0,d.jsx)("html",{lang:"fr",children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 min-h-screen`,children:(0,d.jsxs)("div",{className:"relative min-h-screen",children:[(0,d.jsxs)("div",{className:"absolute inset-0 opacity-30",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-100/50 to-purple-100/50"}),(0,d.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:"radial-gradient(circle at 1px 1px, rgba(99, 102, 241, 0.1) 1px, transparent 0)",backgroundSize:"20px 20px"}})]}),(0,d.jsx)(i.default,{}),(0,d.jsx)("main",{className:"relative z-10",children:a}),(0,d.jsx)("div",{className:"fixed top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl animate-pulse"}),(0,d.jsx)("div",{className:"fixed bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-indigo-400/20 to-pink-400/20 rounded-full blur-xl animate-pulse delay-1000"})]})})})}},99183:(a,b,c)=>{Promise.resolve().then(c.bind(c,86246))},99959:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))}};