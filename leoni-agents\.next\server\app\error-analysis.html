<!DOCTYPE html><!--WsWNPj7rY9b__38HFwNNB--><html lang="fr"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/ca5b0344af6fbe2c.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-6325d97008443c31.js"/><script src="/_next/static/chunks/4bd1b696-cf72ae8a39fa05aa.js" async=""></script><script src="/_next/static/chunks/964-d6e2a37b7965f281.js" async=""></script><script src="/_next/static/chunks/main-app-a4e9da4cb2627252.js" async=""></script><script src="/_next/static/chunks/598-0915b3b2be220ebc.js" async=""></script><script src="/_next/static/chunks/874-437a265a67d6cfee.js" async=""></script><script src="/_next/static/chunks/app/layout-36d5455f7f2eae6a.js" async=""></script><script src="/_next/static/chunks/110-00434082548dfaee.js" async=""></script><script src="/_next/static/chunks/app/error-ba519935ca0333ee.js" async=""></script><script src="/_next/static/chunks/app/not-found-db8394f455dee4e4.js" async=""></script><script src="/_next/static/chunks/app/error-analysis/error-6616152a5f6829ec.js" async=""></script><script src="/_next/static/chunks/app/error-analysis/page-e03e607d99c60644.js" async=""></script><script src="/_next/static/chunks/app/loading-ffc478a7c2c2f15a.js" async=""></script><script src="/_next/static/chunks/app/global-error-a450fa68e65b8143.js" async=""></script><title>Leoni Agents - Système d&#x27;Agents Intelligents</title><meta name="description" content="Système d&#x27;agents pour l&#x27;analyse d&#x27;erreurs et la génération SQL"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_5cfdac __variable_9a8899 antialiased bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 min-h-screen"><div hidden=""><!--$--><!--/$--></div><div class="relative min-h-screen"><div class="absolute inset-0 opacity-30"><div class="absolute inset-0 bg-gradient-to-r from-blue-100/50 to-purple-100/50"></div><div class="absolute inset-0" style="background-image:radial-gradient(circle at 1px 1px, rgba(99, 102, 241, 0.1) 1px, transparent 0);background-size:20px 20px"></div></div><nav class="relative"><div class="bg-gradient-to-r from-[#002857] to-[#003d7a] shadow-xl shadow-[#002857]/20 border-b border-[#003d7a]/30"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex justify-between h-20 items-center"><div class="flex items-center space-x-3"><div class="relative"><div class="w-12 h-12 bg-gradient-to-r from-[#ff7514] to-[#ff8c42] rounded-xl flex items-center justify-center shadow-lg shadow-[#ff7514]/30"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sparkles w-7 h-7 text-white" aria-hidden="true"><path d="M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z"></path><path d="M20 2v4"></path><path d="M22 4h-4"></path><circle cx="4" cy="20" r="2"></circle></svg></div><div class="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-[#ff8c42] to-[#ff7514] rounded-full animate-pulse"></div></div><div><h1 class="text-2xl font-bold text-white">Leoni Agents</h1><p class="text-xs text-[#ff8c42] font-medium">Intelligence Artificielle</p></div></div><div class="hidden sm:flex sm:space-x-2"><a class="group relative inline-flex items-center px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 transform hover:scale-105 text-white/80 hover:text-white hover:bg-white/10 hover:shadow-md" href="/"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-house w-5 h-5 mr-2 transition-all duration-300 text-white/70 group-hover:text-[#ff8c42]" aria-hidden="true"><path d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8"></path><path d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path></svg>Accueil</a><a class="group relative inline-flex items-center px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 transform hover:scale-105 bg-gradient-to-r from-[#ff7514] to-[#ff8c42] text-white shadow-lg shadow-[#ff7514]/25" aria-current="page" href="/error-analysis"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bug w-5 h-5 mr-2 transition-all duration-300 text-white" aria-hidden="true"><path d="m8 2 1.88 1.88"></path><path d="M14.12 3.88 16 2"></path><path d="M9 7.13v-1a3.003 3.003 0 1 1 6 0v1"></path><path d="M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6"></path><path d="M12 20v-9"></path><path d="M6.53 9C4.6 8.8 3 7.1 3 5"></path><path d="M6 13H2"></path><path d="M3 21c0-2.1 1.7-3.9 3.8-4"></path><path d="M20.97 5c0 2.1-1.6 3.8-3.5 4"></path><path d="M22 13h-4"></path><path d="M17.2 17c2.1.1 3.8 1.9 3.8 4"></path></svg>Analyse d&#x27;Erreurs<div class="absolute inset-0 rounded-2xl bg-gradient-to-r from-[#ff7514] to-[#ff8c42] opacity-20 animate-pulse"></div></a><a class="group relative inline-flex items-center px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 transform hover:scale-105 text-white/80 hover:text-white hover:bg-white/10 hover:shadow-md" href="/sql-generation"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database w-5 h-5 mr-2 transition-all duration-300 text-white/70 group-hover:text-[#ff8c42]" aria-hidden="true"><ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M3 5V19A9 3 0 0 0 21 19V5"></path><path d="M3 12A9 3 0 0 0 21 12"></path></svg>Génération SQL</a></div></div></div></div><div class="sm:hidden px-4 pt-2 pb-3 space-y-1"><a class="block pl-3 pr-4 py-2 border-l-4 text-base font-medium border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700" href="/"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-house w-4 h-4 mr-3" aria-hidden="true"><path d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8"></path><path d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path></svg>Accueil</div></a><a class="block pl-3 pr-4 py-2 border-l-4 text-base font-medium bg-blue-50 border-blue-500 text-blue-700" aria-current="page" href="/error-analysis"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bug w-4 h-4 mr-3" aria-hidden="true"><path d="m8 2 1.88 1.88"></path><path d="M14.12 3.88 16 2"></path><path d="M9 7.13v-1a3.003 3.003 0 1 1 6 0v1"></path><path d="M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6"></path><path d="M12 20v-9"></path><path d="M6.53 9C4.6 8.8 3 7.1 3 5"></path><path d="M6 13H2"></path><path d="M3 21c0-2.1 1.7-3.9 3.8-4"></path><path d="M20.97 5c0 2.1-1.6 3.8-3.5 4"></path><path d="M22 13h-4"></path><path d="M17.2 17c2.1.1 3.8 1.9 3.8 4"></path></svg>Analyse d&#x27;Erreurs</div></a><a class="block pl-3 pr-4 py-2 border-l-4 text-base font-medium border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700" href="/sql-generation"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database w-4 h-4 mr-3" aria-hidden="true"><ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M3 5V19A9 3 0 0 0 21 19V5"></path><path d="M3 12A9 3 0 0 0 21 12"></path></svg>Génération SQL</div></a></div></nav><main class="relative z-10"><!--$?--><template id="B:0"></template><div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100"><div class="text-center"><div class="relative"><div class="w-16 h-16 bg-gradient-to-r from-[#ff7514] to-[#ff8c42] rounded-full flex items-center justify-center mx-auto mb-6 animate-pulse"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sparkles w-8 h-8 text-white animate-spin" aria-hidden="true"><path d="M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z"></path><path d="M20 2v4"></path><path d="M22 4h-4"></path><circle cx="4" cy="20" r="2"></circle></svg></div><div class="absolute inset-0 w-16 h-16 bg-gradient-to-r from-[#ff7514] to-[#ff8c42] rounded-full mx-auto animate-ping opacity-20"></div></div><h2 class="text-2xl font-bold text-[#002857] mb-2">Leoni Agents</h2><p class="text-gray-600 mb-4">Chargement en cours...</p><div class="flex justify-center"><div class="flex space-x-1"><div class="w-2 h-2 bg-[#ff7514] rounded-full animate-bounce"></div><div class="w-2 h-2 bg-[#ff7514] rounded-full animate-bounce" style="animation-delay:0.1s"></div><div class="w-2 h-2 bg-[#ff7514] rounded-full animate-bounce" style="animation-delay:0.2s"></div></div></div></div></div><!--/$--></main><div class="fixed top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl animate-pulse"></div><div class="fixed bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-indigo-400/20 to-pink-400/20 rounded-full blur-xl animate-pulse delay-1000"></div></div><script>requestAnimationFrame(function(){$RT=performance.now()});</script><script src="/_next/static/chunks/webpack-6325d97008443c31.js" id="_R_" async=""></script><div hidden id="S:0"><div class="container mx-auto px-4 py-8"><div class="text-center mb-8"><h1 class="text-4xl font-bold text-gray-900 mb-4">Analyse d&#x27;Erreurs</h1><p class="text-gray-600">Analysez vos fichiers de programme et d&#x27;erreur pour obtenir des diagnostics détaillés.</p></div><div class="grid lg:grid-cols-2 gap-8"><div class="space-y-6"><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text w-5 h-5 mr-2" aria-hidden="true"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="M10 9H8"></path><path d="M16 13H8"></path><path d="M16 17H8"></path></svg>Fichier Programme</h3><p class="text-sm text-muted-foreground">Téléchargez ou collez le contenu de votre fichier programme</p></div><div class="p-6 pt-0 space-y-4"><div><input type="file" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 mb-2" accept=".txt,.c,.cpp,.ec,.log"/></div><textarea class="flex w-full rounded-md border border-input bg-background px-3 py-2 ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 min-h-[200px] font-mono text-sm" placeholder="Ou collez le contenu du fichier programme ici..."></textarea></div></div><div class="rounded-lg border bg-card text-card-foreground shadow-sm"><div class="flex flex-col space-y-1.5 p-6"><h3 class="text-2xl font-semibold leading-none tracking-tight flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-alert w-5 h-5 mr-2" aria-hidden="true"><circle cx="12" cy="12" r="10"></circle><line x1="12" x2="12" y1="8" y2="12"></line><line x1="12" x2="12.01" y1="16" y2="16"></line></svg>Fichier d&#x27;Erreur</h3><p class="text-sm text-muted-foreground">Téléchargez ou collez le contenu de votre fichier d&#x27;erreur (optionnel - l&#x27;analyse statique sera effectuée si aucun fichier d&#x27;erreur n&#x27;est fourni)</p></div><div class="p-6 pt-0 space-y-4"><div><input type="file" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 mb-2" accept=".txt,.error,.log"/></div><textarea class="flex w-full rounded-md border border-input bg-background px-3 py-2 ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 min-h-[200px] font-mono text-sm" placeholder="Ou collez le contenu du fichier d&#x27;erreur ici... (Laissez vide pour une analyse statique du code uniquement)"></textarea></div></div><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-11 rounded-md px-8 w-full" disabled=""><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bug w-5 h-5 mr-2" aria-hidden="true"><path d="m8 2 1.88 1.88"></path><path d="M14.12 3.88 16 2"></path><path d="M9 7.13v-1a3.003 3.003 0 1 1 6 0v1"></path><path d="M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6"></path><path d="M12 20v-9"></path><path d="M6.53 9C4.6 8.8 3 7.1 3 5"></path><path d="M6 13H2"></path><path d="M3 21c0-2.1 1.7-3.9 3.8-4"></path><path d="M20.97 5c0 2.1-1.6 3.8-3.5 4"></path><path d="M22 13h-4"></path><path d="M17.2 17c2.1.1 3.8 1.9 3.8 4"></path></svg>Analyse Statique du Code</button><div class="text-center text-sm text-gray-600 mt-2"><span class="flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text w-4 h-4 mr-1" aria-hidden="true"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="M10 9H8"></path><path d="M16 13H8"></path><path d="M16 17H8"></path></svg>Analyse statique du code uniquement</span></div></div><div><div class="text-card-foreground bg-white/60 backdrop-blur-xl border-0 shadow-xl rounded-3xl"><div class="p-6 text-center py-16"><div class="relative mb-6"><div class="w-24 h-24 mx-auto bg-gradient-to-r from-gray-200 to-gray-300 rounded-3xl flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bug w-12 h-12 text-gray-400" aria-hidden="true"><path d="m8 2 1.88 1.88"></path><path d="M14.12 3.88 16 2"></path><path d="M9 7.13v-1a3.003 3.003 0 1 1 6 0v1"></path><path d="M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6"></path><path d="M12 20v-9"></path><path d="M6.53 9C4.6 8.8 3 7.1 3 5"></path><path d="M6 13H2"></path><path d="M3 21c0-2.1 1.7-3.9 3.8-4"></path><path d="M20.97 5c0 2.1-1.6 3.8-3.5 4"></path><path d="M22 13h-4"></path><path d="M17.2 17c2.1.1 3.8 1.9 3.8 4"></path></svg></div><div class="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-pulse"></div></div><h3 class="text-xl font-bold text-gray-700 mb-3">Prêt pour l&#x27;Analyse</h3><p class="text-gray-500 max-w-md mx-auto leading-relaxed">Les résultats de l&#x27;analyse apparaîtront ici une fois que vous aurez téléchargé vos fichiers et lancé l&#x27;analyse.</p><div class="mt-6 flex justify-center space-x-4 text-sm text-gray-400"><span>🔍 Détection automatique</span><span>📍 Localisation précise</span><span>💡 Solutions expertes</span></div></div></div></div></div></div><!--$--><!--/$--></div><script>$RB=[];$RV=function(b){$RT=performance.now();for(var a=0;a<b.length;a+=2){var c=b[a],e=b[a+1];null!==e.parentNode&&e.parentNode.removeChild(e);var f=c.parentNode;if(f){var g=c.previousSibling,h=0;do{if(c&&8===c.nodeType){var d=c.data;if("/$"===d||"/&"===d)if(0===h)break;else h--;else"$"!==d&&"$?"!==d&&"$~"!==d&&"$!"!==d&&"&"!==d||h++}d=c.nextSibling;f.removeChild(c);c=d}while(c);for(;e.firstChild;)f.insertBefore(e.firstChild,c);g.data="$";g._reactRetry&&g._reactRetry()}}b.length=0};
$RC=function(b,a){if(a=document.getElementById(a))(b=document.getElementById(b))?(b.previousSibling.data="$~",$RB.push(b,a),2===$RB.length&&(b="number"!==typeof $RT?0:$RT,a=performance.now(),setTimeout($RV.bind(null,$RB),2300>a&&2E3<a?2300-a:b+300-a))):a.parentNode.removeChild(a)};$RC("B:0","S:0")</script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[993,[\"598\",\"static/chunks/598-0915b3b2be220ebc.js\",\"874\",\"static/chunks/874-437a265a67d6cfee.js\",\"177\",\"static/chunks/app/layout-36d5455f7f2eae6a.js\"],\"default\"]\n3:I[7555,[],\"\"]\n4:I[1901,[\"598\",\"static/chunks/598-0915b3b2be220ebc.js\",\"110\",\"static/chunks/110-00434082548dfaee.js\",\"39\",\"static/chunks/app/error-ba519935ca0333ee.js\"],\"default\"]\n5:I[1295,[],\"\"]\n6:I[1544,[\"598\",\"static/chunks/598-0915b3b2be220ebc.js\",\"874\",\"static/chunks/874-437a265a67d6cfee.js\",\"345\",\"static/chunks/app/not-found-db8394f455dee4e4.js\"],\"default\"]\n7:I[2467,[\"598\",\"static/chunks/598-0915b3b2be220ebc.js\",\"874\",\"static/chunks/874-437a265a67d6cfee.js\",\"110\",\"static/chunks/110-00434082548dfaee.js\",\"973\",\"static/chunks/app/error-analysis/error-6616152a5f6829ec.js\"],\"default\"]\n8:I[894,[],\"ClientPageRoot\"]\n9:I[3516,[\"598\",\"static/chunks/598-0915b3b2be220ebc.js\",\"110\",\"static/chunks/110-00434082548dfaee.js\",\"164\",\"static/chunks/app/error-analysis/page-e03e607d99c60644.js\"],\"default\"]\nc:I[9665,[],\"OutletBoundary\"]\ne:I[4911,[],\"AsyncMetadataOutlet\"]\n10:I[8460,[\"209\",\"static/chunks/app/loading-ffc478a7c2c2f15a.js\"],\"default\"]\n11:I[9665,[],\"ViewportBoundary\"]\n13:I[9665,[],\"MetadataBoundary\"]\n14:\"$Sreact.suspense\"\n16:I[8385,[\"219\",\"static/chunks/app/global-error-a450fa68e65b8143.js\"],\"default\"]\n:HL[\"/_next/static/css/ca5b0344af6fbe2c.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"WsWNPj7rY9b__38HFwNNB\",\"p\":\"\",\"c\":[\"\",\"error-analysis\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"error-analysis\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/ca5b0344af6fbe2c.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"fr\",\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_5cfdac __variable_9a8899 antialiased bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 min-h-screen\",\"children\":[\"$\",\"div\",null,{\"className\":\"relative min-h-screen\",\"children\":[[\"$\",\"div\",null,{\"className\":\"absolute inset-0 opacity-30\",\"children\":[[\"$\",\"div\",null,{\"className\":\"absolute inset-0 bg-gradient-to-r from-blue-100/50 to-purple-100/50\"}],[\"$\",\"div\",null,{\"className\":\"absolute inset-0\",\"style\":{\"backgroundImage\":\"radial-gradient(circle at 1px 1px, rgba(99, 102, 241, 0.1) 1px, transparent 0)\",\"backgroundSize\":\"20px 20px\"}}]]}],[\"$\",\"$L2\",null,{}],[\"$\",\"main\",null,{\"className\":\"relative z-10\",\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$4\",\"errorStyles\":[],\"errorScripts\":[],\"template\":[\"$\",\"$L5\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"$L6\",null,{}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"div\",null,{\"className\":\"fixed top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl animate-pulse\"}],[\"$\",\"div\",null,{\"className\":\"fixed bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-indigo-400/20 to-pink-400/20 rounded-full blur-xl animate-pulse delay-1000\"}]]}]}]}]]}],{\"children\":[\"error-analysis\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$7\",\"errorStyles\":[],\"errorScripts\":[],\"template\":[\"$\",\"$L5\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L8\",null,{\"Component\":\"$9\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@a\",\"$@b\"]}],null,[\"$\",\"$Lc\",null,{\"children\":[\"$Ld\",[\"$\",\"$Le\",null,{\"promise\":\"$@f\"}]]}]]}],{},null,false]},null,false]},[[\"$\",\"$L10\",\"l\",{}],[],[]],false],[\"$\",\"$1\",\"h\",{\"children\":[null,[[\"$\",\"$L11\",null,{\"children\":\"$L12\"}],null],[\"$\",\"$L13\",null,{\"children\":[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$14\",null,{\"fallback\":null,\"children\":\"$L15\"}]}]}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$16\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"a:{}\nb:\"$0:f:0:1:2:children:2:children:1:props:children:0:props:params\"\n"])</script><script>self.__next_f.push([1,"12:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nd:null\n"])</script><script>self.__next_f.push([1,"17:I[8175,[],\"IconMark\"]\nf:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Leoni Agents - Système d'Agents Intelligents\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Système d'agents pour l'analyse d'erreurs et la génération SQL\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}],[\"$\",\"$L17\",\"3\",{}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"15:\"$f:metadata\"\n"])</script></body></html>