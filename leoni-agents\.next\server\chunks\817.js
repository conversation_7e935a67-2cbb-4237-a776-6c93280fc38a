exports.id=817,exports.ids=[817],exports.modules={10974:(a,b,c)=>{"use strict";c.d(b,{W8:()=>j,cn:()=>f,kx:()=>g,nl:()=>k});var d=c(75986),e=c(8974);function f(...a){return(0,e.QP)((0,d.$)(a))}function g(a){return a.split("\n").filter(a=>a.trim()).map((a,b)=>{let c=a.match(/^(\d{2}\.\d{2}\.\d{4} \d{2}:\d{2}:\d{2})\s+(\w+)\s+(.+)$/);if(c){let a,b,[,d,e,f]=c,g=f.match(/task \[([^\]]+)\]/);for(let b of[/(?:line|ligne)[:\s]+(\d+)/i,/\[.*(?:line|ligne)[:\s]+(\d+)/i,/caofors\.ec line: (\d+)/,/at line (\d+)/i,/error on line (\d+)/i,/(\d+):\d+:/,/line (\d+) column \d+/i,/\((\d+),\d+\)/,/:\s*(\d+)\s*:/,/ligne\s+(\d+)/i,/row\s+(\d+)/i,/position\s+(\d+)/i]){let c=f.match(b);if(c){a=parseInt(c[1]);break}}for(let a of[/([a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z]+)/,/in file ([^\s]+)/i,/file "([^"]+)"/i,/([^\s]+\.(?:c|cpp|h|hpp|ec|sql|js|ts|py|java|cs))/i]){let c=f.match(a);if(c){b=c[1];break}}let j=h(f),k=i(e,f);return{timestamp:d,level:e,message:f,task:g?g[1]:void 0,lineNumber:a,errorType:j,severity:k,fileName:b}}for(let b of[/^(ERROR|WARNING|INFO|DEBUG):\s*(.+?)(?:\s+at\s+line\s+(\d+))?$/i,/^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})\s+(\w+)\s+(.+)$/,/^(\w+):\s*(.+)$/]){let c=a.match(b);if(c){let b=c[1]||c[2]||"UNKNOWN",d=c[2]||c[3]||c[1]||a,e=c[3]?parseInt(c[3]):void 0;return{timestamp:c[1]?.includes("T")?c[1]:"",level:b,message:d,lineNumber:e,errorType:h(d),severity:i(b,d)}}}return{timestamp:"",level:"UNKNOWN",message:a,lineNumber:b+1,errorType:"UNKNOWN",severity:"LOW"}})}function h(a){for(let{pattern:b,type:c}of[{pattern:/variable.*not.*(?:initialized|declared|defined)/i,type:"VARIABLE_NOT_INITIALIZED"},{pattern:/undefined.*variable/i,type:"UNDEFINED_VARIABLE"},{pattern:/syntax.*error/i,type:"SYNTAX_ERROR"},{pattern:/compilation.*error/i,type:"COMPILATION_ERROR"},{pattern:/runtime.*error/i,type:"RUNTIME_ERROR"},{pattern:/null.*pointer/i,type:"NULL_POINTER"},{pattern:/memory.*leak/i,type:"MEMORY_LEAK"},{pattern:/buffer.*overflow/i,type:"BUFFER_OVERFLOW"},{pattern:/division.*by.*zero/i,type:"DIVISION_BY_ZERO"},{pattern:/file.*not.*found/i,type:"FILE_NOT_FOUND"},{pattern:/permission.*denied/i,type:"PERMISSION_DENIED"},{pattern:/connection.*failed/i,type:"CONNECTION_ERROR"},{pattern:/timeout/i,type:"TIMEOUT_ERROR"},{pattern:/locked.*already.*runs/i,type:"RESOURCE_LOCKED"},{pattern:/sql.*error/i,type:"SQL_ERROR"},{pattern:/database.*error/i,type:"DATABASE_ERROR"},{pattern:/assertion.*failed/i,type:"ASSERTION_FAILED"},{pattern:/stack.*overflow/i,type:"STACK_OVERFLOW"},{pattern:/out.*of.*memory/i,type:"OUT_OF_MEMORY"},{pattern:/invalid.*argument/i,type:"INVALID_ARGUMENT"},{pattern:/type.*mismatch/i,type:"TYPE_MISMATCH"}])if(b.test(a))return c;return"GENERAL_ERROR"}function i(a,b){let c={DEBUG:"LOW",INFO:"LOW",WARNING:"MEDIUM",WARN:"MEDIUM",ERROR:"HIGH",FATAL:"CRITICAL",CRITICAL:"CRITICAL"}[a.toUpperCase()]||"MEDIUM";return[/crash/i,/fatal/i,/critical/i,/system.*failure/i,/memory.*corruption/i,/security.*breach/i].some(a=>a.test(b))?c="CRITICAL":[/error/i,/exception/i,/failed/i,/abort/i,/null.*pointer/i,/buffer.*overflow/i,/stack.*overflow/i].some(a=>a.test(b))?c="LOW"===c?"HIGH":c:[/warning/i,/info/i,/debug/i,/notice/i].some(a=>a.test(b))&&(c="HIGH"===c?"MEDIUM":c),c}function j(a,b,c=2){let d=a.split("\n"),e=d[b-1]||"",f=Math.max(0,b-c-1),g=Math.min(d.length,b+c),h=[];for(let a=f;a<g;a++)h.push({number:a+1,content:d[a]||"",isTarget:a===b-1});return{targetLine:e,contextLines:h,analysis:k(d,b,c)}}function k(a,b,c=5){let d=Math.max(0,b-c-1),e=Math.min(a.length,b+c),f=a.slice(d,e).join("\n"),g=a[b-1]||"",h=function(a){let b=new Set;return[/(?:int|float|double|char|long|short|unsigned|signed|bool|void)\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,/(?:var|let|const)\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,/([a-zA-Z_][a-zA-Z0-9_]*)\s*=/g,/\(\s*([a-zA-Z_][a-zA-Z0-9_]*(?:\s*,\s*[a-zA-Z_][a-zA-Z0-9_]*)*)\s*\)/g].forEach(c=>{let d;for(;null!==(d=c.exec(a));)d[1]&&d[1].split(",").forEach(a=>{let c=a.trim().split(/\s+/).pop();c&&c.length>1&&b.add(c)})}),Array.from(b)}(f),i=function(a){let b=new Set;return[/(?:int|float|double|char|long|short|unsigned|signed|bool|void)\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g,/([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g,/function\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,/\.([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g].forEach(c=>{let d;for(;null!==(d=c.exec(a));)d[1]&&d[1].length>1&&b.add(d[1])}),Array.from(b)}(f),j=function(a,b){let c=[];return[{pattern:/([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*[^;]*$/,message:"Variable potentiellement non initialis\xe9e ou assignation incompl\xe8te"},{pattern:/\*\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*(?![=])/,message:"D\xe9r\xe9f\xe9rencement de pointeur - v\xe9rifier si le pointeur est NULL"},{pattern:/\[\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\]/,message:"Acc\xe8s tableau - v\xe9rifier les limites d'index"},{pattern:/\/\*.*\*\//,message:"Code comment\xe9 - peut indiquer du code probl\xe9matique"},{pattern:/TODO|FIXME|HACK|BUG/i,message:"Commentaire indiquant un probl\xe8me connu"},{pattern:/malloc|calloc|free/,message:"Gestion m\xe9moire manuelle - v\xe9rifier les fuites m\xe9moire"},{pattern:/strcpy|strcat|sprintf/,message:"Fonction potentiellement dangereuse - risque de buffer overflow"}].forEach(b=>{b.pattern.test(a)&&c.push(b.message)}),b.includes("EXEC SQL")&&!b.includes("SQLCODE_ERROR")&&c.push("Requ\xeate SQL sans v\xe9rification d'erreur appropri\xe9e"),a.includes("=")&&!a.includes(";")&&c.push("Assignation sans point-virgule terminal"),c}(g,f),l=function(a,b,c){let d=[];return c.some(a=>a.includes("non initialis\xe9e"))&&(d.push("Initialiser la variable avant utilisation"),d.push("V\xe9rifier la d\xe9claration de la variable")),c.some(a=>a.includes("pointeur"))&&(d.push("Ajouter une v\xe9rification NULL avant d\xe9r\xe9f\xe9rencement"),d.push("Utiliser des pointeurs intelligents si possible")),c.some(a=>a.includes("tableau"))&&(d.push("V\xe9rifier que l'index est dans les limites du tableau"),d.push("Utiliser des fonctions s\xe9curis\xe9es pour l'acc\xe8s aux tableaux")),a.includes("EXEC SQL")&&(d.push("Ajouter SQLCODE_ERROR_PUTERRDB_RET apr\xe8s la requ\xeate SQL"),d.push("V\xe9rifier le code de retour SQL")),0===a.trim().length&&d.push("Ligne vide - v\xe9rifier si du code manque"),(a.includes("//")||a.includes("/*"))&&d.push("Code comment\xe9 - v\xe9rifier si c'est intentionnel"),d}(g,0,j);return{variables:h,functions:i,potentialIssues:j,suggestions:l}}},17621:(a,b,c)=>{"use strict";c.d(b,{w:()=>h});var d=c(33509),e=c(10974),f=c(46571);let g={id:"error-analysis-agent",name:"Agent d'Analyse d'Erreurs",description:"Analyse les fichiers de programme et d'erreur pour d\xe9tecter les erreurs, leurs emplacements et proposer des solutions",role:"Expert en analyse d'erreurs et d\xe9bogage de programmes",goal:"Identifier, localiser et proposer des solutions pour les erreurs dans les programmes",prompt:`Tu es un expert en analyse d'erreurs et d\xe9bogage de programmes avec des capacit\xe9s avanc\xe9es d'analyse statique.
  Ton r\xf4le est d'analyser les fichiers de programme et les fichiers d'erreur pour :
  1. D\xe9tecter et identifier les erreurs avec pr\xe9cision
  2. Localiser exactement o\xf9 elles se produisent (ligne, colonne si possible)
  3. Analyser le contexte du code autour des erreurs
  4. Expliquer les causes possibles avec des d\xe9tails techniques
  5. Proposer des solutions concr\xe8tes et d\xe9taill\xe9es
  6. Sugg\xe9rer des corrections automatiques quand c'est possible
  7. Identifier les erreurs li\xe9es ou en cascade
  8. Proposer des am\xe9liorations pr\xe9ventives

  Utilise l'analyse contextuelle fournie (variables, fonctions, probl\xe8mes potentiels) pour enrichir tes diagnostics.
  Sois tr\xe8s pr\xe9cis dans tes analyses et fournis des solutions pratiques et applicables.
  Priorise les erreurs par ordre de s\xe9v\xe9rit\xe9 et d'impact.`,tools:["file-analysis","error-parsing","solution-generation"],utils:["parseErrorFile","formatDate"]};class h{constructor(){this.agent=g}async analyzeFiles(a,b){try{let c,g=(0,e.kx)(b.content),h=this.performStaticAnalysis(a.content),i=this.optimizeContentForAnalysis(a.content,g),j=this.enrichErrorsWithContext(g,a.content),k=this.buildOptimizedPrompt(a,b,i,h,j),l=(0,f.DC)(k,7500);if(!l.isValid)throw console.warn(`Prompt d\xe9passe la limite de tokens: ${l.estimatedTokens} tokens (d\xe9passe de ${l.exceedsBy})`),console.warn(`Recommandation: ${l.recommendation}`),Error(`Prompt trop volumineux: ${l.estimatedTokens} tokens. ${l.recommendation}`);let m=await d.A.chat.completions.create({model:"gpt-4",messages:[{role:"system",content:this.agent.prompt},{role:"user",content:k}],temperature:.3,max_tokens:2e3}),n=m.choices[0]?.message?.content;if(!n)throw Error("Aucune r\xe9ponse re\xe7ue de l'API OpenAI");try{let a=n.match(/\{[\s\S]*\}/);c=a?JSON.parse(a[0]):{summary:n,errors:[],recommendations:[],timestamp:new Date().toISOString()}}catch(a){c={summary:n,errors:[],recommendations:[],timestamp:new Date().toISOString()}}return c.timestamp=new Date().toISOString(),c.errors&&(c.errors=c.errors.map((b,c)=>{let d={...b};if(b.lineNumber&&b.lineNumber>0){d.codeContext=(0,e.W8)(a.content,b.lineNumber);let c=j.find(a=>a.lineNumber===b.lineNumber);c&&(d.autoFixSuggestions=this.generateAutoFixSuggestions(c,a.content))}return d}),c.errors=this.identifyRelatedErrors(c.errors)),c}catch(a){throw console.error("Erreur lors de l'analyse:",a),Error(`Erreur lors de l'analyse des fichiers: ${a instanceof Error?a.message:"Erreur inconnue"}`)}}getAgentInfo(){return this.agent}performStaticAnalysis(a){let b=a.split("\n"),c=b.length,d=new Set,e=new Set,f=[],g=[/(?:int|float|double|char|long|short|unsigned|signed|bool|void)\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g,/function\s+([a-zA-Z_][a-zA-Z0-9_]*)/g],h=[/(?:int|float|double|char|long|short|unsigned|signed|bool)\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,/(?:var|let|const)\s+([a-zA-Z_][a-zA-Z0-9_]*)/g];b.forEach((a,b)=>{g.forEach(b=>{let c;for(;null!==(c=b.exec(a));)d.add(c[1])}),h.forEach(b=>{let c;for(;null!==(c=b.exec(a));)e.add(c[1])}),a.includes("malloc")&&!a.includes("free")&&f.push(`Ligne ${b+1}: Allocation m\xe9moire sans lib\xe9ration visible`),(a.includes("strcpy")||a.includes("strcat"))&&f.push(`Ligne ${b+1}: Fonction potentiellement dangereuse d\xe9tect\xe9e`),(a.includes("TODO")||a.includes("FIXME"))&&f.push(`Ligne ${b+1}: Code incomplet ou probl\xe9matique`)});let i="LOW";return c>1e3||d.size>50?i="HIGH":(c>500||d.size>20)&&(i="MEDIUM"),{totalLines:c,functions:Array.from(d),variables:Array.from(e),potentialIssues:f,complexity:i}}optimizeContentForAnalysis(a,b){let c=a.split("\n"),d=c.length;if(4e3>=(0,f.bP)(a))return a;let e=new Set;b.forEach(a=>{if(a.lineNumber){let c=Math.min(5,Math.floor(4e3/(20*b.length)));for(let b=Math.max(0,a.lineNumber-c);b<=Math.min(d-1,a.lineNumber+c);b++)e.add(b)}});let g=Math.min(20,Math.floor(40));for(let a=0;a<Math.min(g,d);a++)e.add(a);for(let a=Math.max(0,d-g);a<d;a++)e.add(a);let h=Array.from(e).sort((a,b)=>a-b),i="",j=-1;return h.forEach(a=>{a>j+1&&(i+="\n... [LIGNES OMISES] ...\n"),i+=`${a+1}: ${c[a]}
`,j=a}),(0,f.bP)(i)>4e3&&(i=(0,f.Yf)(i,16e3)[0]+"\n\n... [CONTENU TRONQU\xc9 POUR RESPECTER LA LIMITE DE TOKENS] ..."),i}buildOptimizedPrompt(a,b,c,d,e){let g=this.agent.prompt,h=6e3-(0,f.bP)(g),i=[],j=JSON.stringify(e,null,2);i.push({content:`ERREURS PARS\xc9ES AVEC CONTEXTE:
${j}`,priority:1,tokens:(0,f.bP)(j)});let k=`FICHIER D'ERREUR:
Nom: ${b.name}
Contenu:
${b.content}`;i.push({content:k,priority:2,tokens:(0,f.bP)(k)});let l=`FICHIER PROGRAMME:
Nom: ${a.name}
Contenu (optimis\xe9 pour l'analyse):
${c}`;i.push({content:l,priority:3,tokens:(0,f.bP)(l)});let m=`ANALYSE STATIQUE DU PROGRAMME:
${JSON.stringify(d,null,2)}`;i.push({content:m,priority:4,tokens:(0,f.bP)(m)});let n=`
Analyse ces fichiers et fournis une r\xe9ponse JSON structur\xe9e avec:
{
  "summary": "R\xe9sum\xe9 g\xe9n\xe9ral de l'analyse avec priorit\xe9s",
  "errors": [
    {
      "errorType": "Type d'erreur sp\xe9cifique",
      "location": "Emplacement pr\xe9cis avec contexte",
      "description": "Description d\xe9taill\xe9e avec analyse technique",
      "possibleCauses": ["cause1", "cause2"],
      "solutions": ["solution1", "solution2"],
      "severity": "LOW|MEDIUM|HIGH|CRITICAL",
      "lineNumber": 123,
      "fileName": "nom_du_fichier.ext"
    }
  ],
  "recommendations": ["recommandation1", "recommandation2"],
  "preventiveMeasures": ["mesure1", "mesure2"]
}

INSTRUCTIONS:
- Priorise les erreurs par s\xe9v\xe9rit\xe9
- Propose des corrections automatiques
- Identifie les erreurs li\xe9es
- Sois pr\xe9cis sur les num\xe9ros de ligne`;i.sort((a,b)=>a.priority-b.priority);let o=g+"\n\n";for(let a of i)if(a.tokens<=h)o+=a.content+"\n\n",h-=a.tokens;else if(a.priority<=3){let b=4*h;if(b>100){o+=a.content.substring(0,b)+"\n... [CONTENU TRONQU\xc9] ...\n\n",h=0;break}}return o+n}optimizeContentForStaticAnalysis(a){if(3e3>=(0,f.bP)(a))return a;let b=a.split("\n"),c=b.length,d=Math.min(100,Math.floor(.2*c)),e=Math.min(100,Math.floor(.2*c)),g=Math.min(200,c-d-e),h=[];if(h=h.concat(b.slice(0,d)),g>0){h.push("... [SECTION INTERM\xc9DIAIRE \xc9CHANTILLONN\xc9E] ...");let a=c-e,f=Math.max(1,Math.floor((a-d)/g));for(let c=d;c<a;c+=f)h.length<300&&h.push(`${c+1}: ${b[c]}`)}return e>0&&(h.push("... [SECTION FINALE] ..."),h=h.concat(b.slice(-e).map((a,b)=>`${c-e+b+1}: ${a}`))),h.join("\n")}buildOptimizedStaticAnalysisPrompt(a,b,c,d){let e=this.agent.prompt,g=6e3-(0,f.bP)(e),h=[],i=`FICHIER PROGRAMME:
Nom: ${a.name}
Contenu (optimis\xe9):
${b}`;h.push({content:i,priority:1,tokens:(0,f.bP)(i)});let j=`PROBL\xc8MES D\xc9TECT\xc9S:
${JSON.stringify(d,null,2)}`;h.push({content:j,priority:2,tokens:(0,f.bP)(j)});let k=`ANALYSE STATIQUE:
${JSON.stringify(c,null,2)}`;h.push({content:k,priority:3,tokens:(0,f.bP)(k)});let l=`
ANALYSE STATIQUE SEULE (sans fichier d'erreur):

Effectue une analyse statique compl\xe8te et fournis une r\xe9ponse JSON avec:
{
  "summary": "R\xe9sum\xe9 de l'analyse statique",
  "errors": [
    {
      "errorType": "Type de probl\xe8me",
      "location": "Emplacement",
      "description": "Description",
      "possibleCauses": ["causes"],
      "solutions": ["solutions"],
      "severity": "LOW|MEDIUM|HIGH|CRITICAL",
      "lineNumber": 123
    }
  ],
  "recommendations": ["am\xe9liorations"],
  "codeQuality": {
    "score": 85,
    "strengths": ["points forts"],
    "weaknesses": ["points faibles"],
    "maintainability": "LOW|MEDIUM|HIGH"
  }
}`,m=e+"\n\n";for(let a of(h.sort((a,b)=>a.priority-b.priority),h))if(a.tokens<=g)m+=a.content+"\n\n",g-=a.tokens;else if(a.priority<=2){let b=4*g;if(b>100){m+=a.content.substring(0,b)+"\n... [TRONQU\xc9] ...\n\n",g=0;break}}return m+l}enrichErrorsWithContext(a,b){return a.map(a=>{if(a.lineNumber){let c=(0,e.nl)(b.split("\n"),a.lineNumber);return{...a,context:{variables:c.variables,functions:c.functions,potentialIssues:c.potentialIssues,suggestions:c.suggestions}}}return a})}generateAutoFixSuggestions(a,b){let c=[];if(!a.lineNumber)return c;let d=b.split("\n")[a.lineNumber-1];switch(a.errorType){case"VARIABLE_NOT_INITIALIZED":d.includes("=")&&c.push({type:"REPLACE",description:"Initialiser la variable \xe0 une valeur par d\xe9faut",originalCode:d,suggestedCode:d.replace(/([a-zA-Z_][a-zA-Z0-9_]*)\s*;/,"$1 = 0;"),confidence:"MEDIUM",lineNumber:a.lineNumber});break;case"RESOURCE_LOCKED":c.push({type:"INSERT",description:"Ajouter une v\xe9rification de verrou avant ex\xe9cution",suggestedCode:"if (!is_task_running(task_name)) {",confidence:"HIGH",lineNumber:a.lineNumber});break;case"SQL_ERROR":d.includes("EXEC SQL")&&!d.includes("SQLCODE_ERROR")&&c.push({type:"INSERT",description:"Ajouter la v\xe9rification d'erreur SQL",suggestedCode:'SQLCODE_ERROR_PUTERRDB_RET(__LINE__, "LOG", "caofors.ec", "description");',confidence:"HIGH",lineNumber:a.lineNumber+1})}return c}identifyRelatedErrors(a){return a.map((b,c)=>{let d=[];return a.forEach((a,e)=>{c!==e&&b.lineNumber&&a.lineNumber&&5>=Math.abs(b.lineNumber-a.lineNumber)&&d.push(e)}),a.forEach((a,e)=>{c!==e&&b.errorType===a.errorType&&d.push(e)}),{...b,relatedErrors:[...new Set(d)]}})}async analyzeCodeOnly(a){try{let b,c=this.performStaticAnalysis(a.content),g=this.detectStaticIssues(a.content),h=this.optimizeContentForStaticAnalysis(a.content),i=this.buildOptimizedStaticAnalysisPrompt(a,h,c,g),j=(0,f.DC)(i,7500);if(!j.isValid)throw console.warn(`Prompt d'analyse statique d\xe9passe la limite: ${j.estimatedTokens} tokens`),console.warn(`Recommandation: ${j.recommendation}`),Error(`Prompt trop volumineux pour l'analyse statique: ${j.estimatedTokens} tokens. ${j.recommendation}`);let k=await d.A.chat.completions.create({model:"gpt-4",messages:[{role:"system",content:this.agent.prompt},{role:"user",content:i}],temperature:.2,max_tokens:3e3}),l=k.choices[0]?.message?.content;if(!l)throw Error("Aucune r\xe9ponse re\xe7ue de l'API OpenAI");try{let a=l.match(/\{[\s\S]*\}/);b=a?JSON.parse(a[0]):{summary:l,errors:[],recommendations:[],timestamp:new Date().toISOString()}}catch(a){b={summary:l,errors:[],recommendations:[],timestamp:new Date().toISOString()}}return b.timestamp=new Date().toISOString(),b.errors&&(b.errors=b.errors.map(b=>{if(b.lineNumber&&b.lineNumber>0){let c=(0,e.W8)(a.content,b.lineNumber);return{...b,codeContext:c}}return b})),b}catch(a){throw console.error("Erreur lors de l'analyse statique:",a),Error(`Erreur lors de l'analyse statique: ${a instanceof Error?a.message:"Erreur inconnue"}`)}}detectStaticIssues(a){let b=[],c=a.split("\n");return c.forEach((d,e)=>{let f=e+1;/strcpy|strcat|sprintf|gets/.test(d)&&b.push({type:"SECURITY_RISK",description:"Utilisation de fonction potentiellement dangereuse",lineNumber:f,severity:"HIGH"}),/malloc|calloc/.test(d)&&!a.includes("free")&&b.push({type:"MEMORY_LEAK",description:"Allocation m\xe9moire sans lib\xe9ration visible",lineNumber:f,severity:"MEDIUM"}),/(?:int|float|double|char)\s+[a-zA-Z_][a-zA-Z0-9_]*\s*;/.test(d)&&b.push({type:"UNINITIALIZED_VARIABLE",description:"Variable d\xe9clar\xe9e mais non initialis\xe9e",lineNumber:f,severity:"MEDIUM"}),/\/\*.*\*\//.test(d)&&d.includes("TODO")&&b.push({type:"DEAD_CODE",description:"Code comment\xe9 ou incomplet",lineNumber:f,severity:"LOW"}),(d.match(/if|while|for|switch/g)||[]).length>3&&b.push({type:"HIGH_COMPLEXITY",description:"Ligne avec complexit\xe9 cyclomatique \xe9lev\xe9e",lineNumber:f,severity:"LOW"}),/EXEC SQL/.test(d)&&!c[e+1]?.includes("SQLCODE_ERROR")&&b.push({type:"SQL_NO_ERROR_CHECK",description:"Requ\xeate SQL sans v\xe9rification d'erreur",lineNumber:f,severity:"HIGH"})}),b}}},33509:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=new(c(40694)).Ay({apiKey:process.env.OPENAI_API_KEY||"********************************************************************************************************************************************************************",dangerouslyAllowBrowser:!0})},46571:(a,b,c)=>{"use strict";c.d(b,{DC:()=>g,Yf:()=>d,bP:()=>e,mC:()=>f});function d(a,b=2e4){if(a.length<=b)return[a];let c=[],e=0;for(;e<a.length;){let d=e+b;if(d<a.length){let b=a.lastIndexOf("\n",d);if(b>e)d=b;else{let b=a.lastIndexOf(" ",d);b>e&&(d=b)}}c.push(a.slice(e,d).trim()),e=d+1}return c.filter(a=>a.length>0)}function e(a){return Math.ceil(a.length/4)}function f(a){let b=a.length;e(a);let c=a,d=!1;return e(a)>5e3&&(c=function(a){let b=a.split("\n"),c=[],d=["table","column","field","primary key","foreign key","index","constraint","relationship","join","select","insert","update","delete","create","alter","drop","database","schema","view","procedure","function","trigger","unique","not null","default","auto_increment"];for(let a of b){let b=a.toLowerCase();d.some(a=>b.includes(a))?c.push(a):a.trim().length>0&&a.trim().length<100&&(a.includes(":")||a.match(/^[A-Z]/)||a.includes("="))&&c.push(a)}let e=c.join("\n");if(e.length>12e3){let a=Math.floor(.4*c.length);e=c.slice(0,a).join("\n")+"\n\n[... Sp\xe9cification tronqu\xe9e pour respecter les limites de tokens ...]"}return e.length>12e3&&(e=e.slice(0,12e3)+"\n\n[... Texte tronqu\xe9 ...]"),e||a.slice(0,12e3)+"\n\n[... Texte tronqu\xe9 ...]"}(a),d=!0),{processedText:c,wasTruncated:d,originalLength:b,processedLength:c.length,estimatedTokens:e(c)}}function g(a,b=8e3){let c=e(a),d=c<=b,f=Math.max(0,c-b),h="";return h=d?"Prompt dans les limites acceptables":f<1e3?"R\xe9duire l\xe9g\xe8rement le contenu ou utiliser des r\xe9sum\xe9s plus courts":f<3e3?"Diviser le prompt en plusieurs parties ou tronquer significativement":"Restructurer compl\xe8tement l'approche - contenu trop volumineux",{isValid:d,estimatedTokens:c,exceedsBy:f,recommendation:h}}},78335:()=>{},96487:()=>{}};