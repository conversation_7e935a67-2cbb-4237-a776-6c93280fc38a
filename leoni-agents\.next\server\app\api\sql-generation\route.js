(()=>{var a={};a.id=845,a.ids=[845],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33509:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=new(c(40694)).Ay({apiKey:process.env.OPENAI_API_KEY||"********************************************************************************************************************************************************************",dangerouslyAllowBrowser:!0})},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45655:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>G,patchFetch:()=>F,routeModule:()=>B,serverHooks:()=>E,workAsyncStorage:()=>C,workUnitAsyncStorage:()=>D});var d={};c.r(d),c.d(d,{GET:()=>A,POST:()=>z});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(33509);let w={id:"sql-generation-agent",name:"Agent de G\xe9n\xe9ration SQL",description:"G\xe9n\xe8re des scripts SQL \xe0 partir de sp\xe9cifications fonctionnelles",role:"Expert en bases de donn\xe9es et g\xe9n\xe9ration de scripts SQL",goal:"Cr\xe9er des scripts SQL optimis\xe9s et bien structur\xe9s \xe0 partir de sp\xe9cifications",prompt:`Tu es un expert en bases de donn\xe9es et g\xe9n\xe9ration de scripts SQL.
  Ton r\xf4le est de transformer des sp\xe9cifications fonctionnelles en scripts SQL optimis\xe9s.
  
  Tu dois :
  1. Analyser les sp\xe9cifications fournies
  2. Identifier les entit\xe9s, relations et contraintes
  3. G\xe9n\xe9rer un script SQL complet et optimis\xe9
  4. Inclure des commentaires explicatifs
  5. Respecter les bonnes pratiques SQL
  6. Proposer des index appropri\xe9s si n\xe9cessaire
  
  Tu dois fournir un script SQL professionnel avec des explications d\xe9taill\xe9es.`,tools:["sql-generation","schema-analysis","optimization"],utils:["formatDate","validateSQL"]};class x{constructor(){this.agent=w}async generateSQL(a){try{let b,{specification:c,databaseType:d="mysql",includeComments:e=!0}=a,f=`Tu es un expert SQL. G\xe9n\xe8re un script ${d.toUpperCase()} bas\xe9 sur cette sp\xe9cification.

SP\xc9CIFICATION:
${c}

R\xe9ponds en JSON:
{
  "sql": "Script SQL complet",
  "explanation": "Explication concise",
  "tables": ["table1", "table2"],
  "operations": ["CREATE", "INSERT"]
}

Le script SQL doit \xeatre optimis\xe9 pour ${d} et inclure :
- Cr\xe9ation des tables avec contraintes appropri\xe9es
- Index recommand\xe9s
- Commentaires explicatifs ${e?"":"(optionnels)"}
- Donn\xe9es d'exemple si pertinent
- Proc\xe9dures stock\xe9es si n\xe9cessaire`,g=await v.A.chat.completions.create({model:"gpt-4",messages:[{role:"system",content:this.agent.prompt},{role:"user",content:f}],temperature:.2,max_tokens:3e3}),h=g.choices[0]?.message?.content;if(!h)throw Error("Aucune r\xe9ponse re\xe7ue de l'API OpenAI");try{let a=h.match(/\{[\s\S]*\}/);b=a?JSON.parse(a[0]):{sql:h,explanation:"Script SQL g\xe9n\xe9r\xe9 \xe0 partir de la sp\xe9cification",tables:[],operations:[],timestamp:new Date().toISOString()}}catch(d){let a=h.match(/```sql\n([\s\S]*?)\n```/)||h.match(/```\n([\s\S]*?)\n```/),c=a?a[1]:h;b={sql:c,explanation:h,tables:this.extractTables(c),operations:this.extractOperations(c),timestamp:new Date().toISOString()}}return b.timestamp=new Date().toISOString(),b}catch(a){throw console.error("Erreur lors de la g\xe9n\xe9ration SQL:",a),Error(`Erreur lors de la g\xe9n\xe9ration du script SQL: ${a instanceof Error?a.message:"Erreur inconnue"}`)}}extractTables(a){let b=a.match(/CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?`?(\w+)`?/gi);return b?b.map(a=>{let b=a.match(/CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?`?(\w+)`?/i);return b?b[1]:""}).filter(Boolean):[]}extractOperations(a){let b=new Set,c=a.match(/\b(CREATE|INSERT|UPDATE|DELETE|SELECT|ALTER|DROP|INDEX)\b/gi);return c&&c.forEach(a=>b.add(a.toUpperCase())),Array.from(b)}getAgentInfo(){return this.agent}}var y=c(46571);async function z(a){try{let{specification:b,databaseType:c,includeComments:d}=await a.json();if(!b||!b.trim())return u.NextResponse.json({error:"La sp\xe9cification est requise"},{status:400});let e=(0,y.mC)(b);console.log(`API SQL: Sp\xe9cification ${e.wasTruncated?"tronqu\xe9e":"normale"}`),console.log(`Longueur: ${e.originalLength} → ${e.processedLength} caract\xe8res`),console.log(`Tokens estim\xe9s: ${e.estimatedTokens}`);let f=new x,g=await f.generateSQL({specification:e.processedText,databaseType:c||"mysql",includeComments:!1!==d});return u.NextResponse.json({success:!0,data:g,timestamp:new Date().toISOString()})}catch(a){return console.error("Erreur lors de la g\xe9n\xe9ration SQL:",a),u.NextResponse.json({success:!1,error:a instanceof Error?a.message:"Erreur interne du serveur",timestamp:new Date().toISOString()},{status:500})}}async function A(){let a=new x().getAgentInfo();return u.NextResponse.json({success:!0,data:a,timestamp:new Date().toISOString()})}let B=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/sql-generation/route",pathname:"/api/sql-generation",filename:"route",bundlePath:"app/api/sql-generation/route"},distDir:".next",projectDir:"",resolvedPagePath:"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\api\\sql-generation\\route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:C,workUnitAsyncStorage:D,serverHooks:E}=B;function F(){return(0,g.patchFetch)({workAsyncStorage:C,workUnitAsyncStorage:D})}async function G(a,b,c){var d;let e="/api/sql-generation/route";"/index"===e&&(e="/");let g=await B.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:A,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(y.dynamicRoutes[E]||y.routes[D]);if(F&&!x){let a=!!y.routes[D],b=y.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||B.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===B.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>B.onRequestError(a,b,d,z)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>B.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&A&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await B.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:A})},z),b}},l=await B.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",A?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await B.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:A})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},46571:(a,b,c)=>{"use strict";c.d(b,{DC:()=>g,Yf:()=>d,bP:()=>e,mC:()=>f});function d(a,b=2e4){if(a.length<=b)return[a];let c=[],e=0;for(;e<a.length;){let d=e+b;if(d<a.length){let b=a.lastIndexOf("\n",d);if(b>e)d=b;else{let b=a.lastIndexOf(" ",d);b>e&&(d=b)}}c.push(a.slice(e,d).trim()),e=d+1}return c.filter(a=>a.length>0)}function e(a){return Math.ceil(a.length/4)}function f(a){let b=a.length;e(a);let c=a,d=!1;return e(a)>5e3&&(c=function(a){let b=a.split("\n"),c=[],d=["table","column","field","primary key","foreign key","index","constraint","relationship","join","select","insert","update","delete","create","alter","drop","database","schema","view","procedure","function","trigger","unique","not null","default","auto_increment"];for(let a of b){let b=a.toLowerCase();d.some(a=>b.includes(a))?c.push(a):a.trim().length>0&&a.trim().length<100&&(a.includes(":")||a.match(/^[A-Z]/)||a.includes("="))&&c.push(a)}let e=c.join("\n");if(e.length>12e3){let a=Math.floor(.4*c.length);e=c.slice(0,a).join("\n")+"\n\n[... Sp\xe9cification tronqu\xe9e pour respecter les limites de tokens ...]"}return e.length>12e3&&(e=e.slice(0,12e3)+"\n\n[... Texte tronqu\xe9 ...]"),e||a.slice(0,12e3)+"\n\n[... Texte tronqu\xe9 ...]"}(a),d=!0),{processedText:c,wasTruncated:d,originalLength:b,processedLength:c.length,estimatedTokens:e(c)}}function g(a,b=8e3){let c=e(a),d=c<=b,f=Math.max(0,c-b),h="";return h=d?"Prompt dans les limites acceptables":f<1e3?"R\xe9duire l\xe9g\xe8rement le contenu ou utiliser des r\xe9sum\xe9s plus courts":f<3e3?"Diviser le prompt en plusieurs parties ou tronquer significativement":"Restructurer compl\xe8tement l'approche - contenu trop volumineux",{isValid:d,estimatedTokens:c,exceedsBy:f,recommendation:h}}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96487:()=>{}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,55,694],()=>b(b.s=45655));module.exports=c})();