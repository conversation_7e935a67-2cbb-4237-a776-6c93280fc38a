(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[39],{1901:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(5155),l=t(2115),a=t(5476),c=t(5339),n=t(3904);function i(e){let{error:s,reset:t}=e;return(0,l.useEffect)(()=>{console.error(s)},[s]),(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100",children:(0,r.jsx)("div",{className:"max-w-md w-full mx-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8 text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r from-[#ff7514] to-[#ff8c42] rounded-full flex items-center justify-center mx-auto mb-6",children:(0,r.jsx)(c.A,{className:"w-8 h-8 text-white"})}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-[#002857] mb-4",children:"Oups ! Une erreur s'est produite"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"Nous rencontrons un probl\xe8me technique temporaire. Veuillez r\xe9essayer dans quelques instants."}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(a.$,{onClick:t,className:"w-full bg-gradient-to-r from-[#ff7514] to-[#ff8c42] hover:from-[#e6650f] hover:to-[#ff7514] text-white",children:[(0,r.jsx)(n.A,{className:"w-4 h-4 mr-2"}),"R\xe9essayer"]}),(0,r.jsx)(a.$,{variant:"outline",onClick:()=>window.location.href="/",className:"w-full border-[#002857] text-[#002857] hover:bg-[#002857] hover:text-white",children:"Retour \xe0 l'accueil"})]}),!1]})})})}},3904:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(9946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},4266:(e,s,t)=>{Promise.resolve().then(t.bind(t,1901))}},e=>{e.O(0,[598,110,441,964,358],()=>e(e.s=4266)),_N_E=e.O()}]);