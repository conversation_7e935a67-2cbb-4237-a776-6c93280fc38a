(()=>{var a={};a.id=974,a.ids=[974],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(a,b,c)=>{let{createProxy:d}=c(39844);a.exports=d("C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\node_modules\\next\\dist\\client\\app-dir\\link.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(a,b,c)=>{"use strict";c.d(b,{W8:()=>j,cn:()=>f,kx:()=>g,nl:()=>k});var d=c(75986),e=c(8974);function f(...a){return(0,e.QP)((0,d.$)(a))}function g(a){return a.split("\n").filter(a=>a.trim()).map((a,b)=>{let c=a.match(/^(\d{2}\.\d{2}\.\d{4} \d{2}:\d{2}:\d{2})\s+(\w+)\s+(.+)$/);if(c){let a,b,[,d,e,f]=c,g=f.match(/task \[([^\]]+)\]/);for(let b of[/(?:line|ligne)[:\s]+(\d+)/i,/\[.*(?:line|ligne)[:\s]+(\d+)/i,/caofors\.ec line: (\d+)/,/at line (\d+)/i,/error on line (\d+)/i,/(\d+):\d+:/,/line (\d+) column \d+/i,/\((\d+),\d+\)/,/:\s*(\d+)\s*:/,/ligne\s+(\d+)/i,/row\s+(\d+)/i,/position\s+(\d+)/i]){let c=f.match(b);if(c){a=parseInt(c[1]);break}}for(let a of[/([a-zA-Z_][a-zA-Z0-9_]*\.[a-zA-Z]+)/,/in file ([^\s]+)/i,/file "([^"]+)"/i,/([^\s]+\.(?:c|cpp|h|hpp|ec|sql|js|ts|py|java|cs))/i]){let c=f.match(a);if(c){b=c[1];break}}let j=h(f),k=i(e,f);return{timestamp:d,level:e,message:f,task:g?g[1]:void 0,lineNumber:a,errorType:j,severity:k,fileName:b}}for(let b of[/^(ERROR|WARNING|INFO|DEBUG):\s*(.+?)(?:\s+at\s+line\s+(\d+))?$/i,/^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})\s+(\w+)\s+(.+)$/,/^(\w+):\s*(.+)$/]){let c=a.match(b);if(c){let b=c[1]||c[2]||"UNKNOWN",d=c[2]||c[3]||c[1]||a,e=c[3]?parseInt(c[3]):void 0;return{timestamp:c[1]?.includes("T")?c[1]:"",level:b,message:d,lineNumber:e,errorType:h(d),severity:i(b,d)}}}return{timestamp:"",level:"UNKNOWN",message:a,lineNumber:b+1,errorType:"UNKNOWN",severity:"LOW"}})}function h(a){for(let{pattern:b,type:c}of[{pattern:/variable.*not.*(?:initialized|declared|defined)/i,type:"VARIABLE_NOT_INITIALIZED"},{pattern:/undefined.*variable/i,type:"UNDEFINED_VARIABLE"},{pattern:/syntax.*error/i,type:"SYNTAX_ERROR"},{pattern:/compilation.*error/i,type:"COMPILATION_ERROR"},{pattern:/runtime.*error/i,type:"RUNTIME_ERROR"},{pattern:/null.*pointer/i,type:"NULL_POINTER"},{pattern:/memory.*leak/i,type:"MEMORY_LEAK"},{pattern:/buffer.*overflow/i,type:"BUFFER_OVERFLOW"},{pattern:/division.*by.*zero/i,type:"DIVISION_BY_ZERO"},{pattern:/file.*not.*found/i,type:"FILE_NOT_FOUND"},{pattern:/permission.*denied/i,type:"PERMISSION_DENIED"},{pattern:/connection.*failed/i,type:"CONNECTION_ERROR"},{pattern:/timeout/i,type:"TIMEOUT_ERROR"},{pattern:/locked.*already.*runs/i,type:"RESOURCE_LOCKED"},{pattern:/sql.*error/i,type:"SQL_ERROR"},{pattern:/database.*error/i,type:"DATABASE_ERROR"},{pattern:/assertion.*failed/i,type:"ASSERTION_FAILED"},{pattern:/stack.*overflow/i,type:"STACK_OVERFLOW"},{pattern:/out.*of.*memory/i,type:"OUT_OF_MEMORY"},{pattern:/invalid.*argument/i,type:"INVALID_ARGUMENT"},{pattern:/type.*mismatch/i,type:"TYPE_MISMATCH"}])if(b.test(a))return c;return"GENERAL_ERROR"}function i(a,b){let c={DEBUG:"LOW",INFO:"LOW",WARNING:"MEDIUM",WARN:"MEDIUM",ERROR:"HIGH",FATAL:"CRITICAL",CRITICAL:"CRITICAL"}[a.toUpperCase()]||"MEDIUM";return[/crash/i,/fatal/i,/critical/i,/system.*failure/i,/memory.*corruption/i,/security.*breach/i].some(a=>a.test(b))?c="CRITICAL":[/error/i,/exception/i,/failed/i,/abort/i,/null.*pointer/i,/buffer.*overflow/i,/stack.*overflow/i].some(a=>a.test(b))?c="LOW"===c?"HIGH":c:[/warning/i,/info/i,/debug/i,/notice/i].some(a=>a.test(b))&&(c="HIGH"===c?"MEDIUM":c),c}function j(a,b,c=2){let d=a.split("\n"),e=d[b-1]||"",f=Math.max(0,b-c-1),g=Math.min(d.length,b+c),h=[];for(let a=f;a<g;a++)h.push({number:a+1,content:d[a]||"",isTarget:a===b-1});return{targetLine:e,contextLines:h,analysis:k(d,b,c)}}function k(a,b,c=5){let d=Math.max(0,b-c-1),e=Math.min(a.length,b+c),f=a.slice(d,e).join("\n"),g=a[b-1]||"",h=function(a){let b=new Set;return[/(?:int|float|double|char|long|short|unsigned|signed|bool|void)\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,/(?:var|let|const)\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,/([a-zA-Z_][a-zA-Z0-9_]*)\s*=/g,/\(\s*([a-zA-Z_][a-zA-Z0-9_]*(?:\s*,\s*[a-zA-Z_][a-zA-Z0-9_]*)*)\s*\)/g].forEach(c=>{let d;for(;null!==(d=c.exec(a));)d[1]&&d[1].split(",").forEach(a=>{let c=a.trim().split(/\s+/).pop();c&&c.length>1&&b.add(c)})}),Array.from(b)}(f),i=function(a){let b=new Set;return[/(?:int|float|double|char|long|short|unsigned|signed|bool|void)\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g,/([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g,/function\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,/\.([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g].forEach(c=>{let d;for(;null!==(d=c.exec(a));)d[1]&&d[1].length>1&&b.add(d[1])}),Array.from(b)}(f),j=function(a,b){let c=[];return[{pattern:/([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*[^;]*$/,message:"Variable potentiellement non initialis\xe9e ou assignation incompl\xe8te"},{pattern:/\*\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*(?![=])/,message:"D\xe9r\xe9f\xe9rencement de pointeur - v\xe9rifier si le pointeur est NULL"},{pattern:/\[\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\]/,message:"Acc\xe8s tableau - v\xe9rifier les limites d'index"},{pattern:/\/\*.*\*\//,message:"Code comment\xe9 - peut indiquer du code probl\xe9matique"},{pattern:/TODO|FIXME|HACK|BUG/i,message:"Commentaire indiquant un probl\xe8me connu"},{pattern:/malloc|calloc|free/,message:"Gestion m\xe9moire manuelle - v\xe9rifier les fuites m\xe9moire"},{pattern:/strcpy|strcat|sprintf/,message:"Fonction potentiellement dangereuse - risque de buffer overflow"}].forEach(b=>{b.pattern.test(a)&&c.push(b.message)}),b.includes("EXEC SQL")&&!b.includes("SQLCODE_ERROR")&&c.push("Requ\xeate SQL sans v\xe9rification d'erreur appropri\xe9e"),a.includes("=")&&!a.includes(";")&&c.push("Assignation sans point-virgule terminal"),c}(g,f),l=function(a,b,c){let d=[];return c.some(a=>a.includes("non initialis\xe9e"))&&(d.push("Initialiser la variable avant utilisation"),d.push("V\xe9rifier la d\xe9claration de la variable")),c.some(a=>a.includes("pointeur"))&&(d.push("Ajouter une v\xe9rification NULL avant d\xe9r\xe9f\xe9rencement"),d.push("Utiliser des pointeurs intelligents si possible")),c.some(a=>a.includes("tableau"))&&(d.push("V\xe9rifier que l'index est dans les limites du tableau"),d.push("Utiliser des fonctions s\xe9curis\xe9es pour l'acc\xe8s aux tableaux")),a.includes("EXEC SQL")&&(d.push("Ajouter SQLCODE_ERROR_PUTERRDB_RET apr\xe8s la requ\xeate SQL"),d.push("V\xe9rifier le code de retour SQL")),0===a.trim().length&&d.push("Ligne vide - v\xe9rifier si du code manque"),(a.includes("//")||a.includes("/*"))&&d.push("Code comment\xe9 - v\xe9rifier si c'est intentionnel"),d}(g,0,j);return{variables:h,functions:i,potentialIssues:j,suggestions:l}}},14484:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>E});var d=c(37413),e=c(4536),f=c.n(e),g=c(61120),h=c(10974);let i=g.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,h.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...b}));i.displayName="Card";let j=g.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,h.cn)("flex flex-col space-y-1.5 p-6",a),...b}));j.displayName="CardHeader";let k=g.forwardRef(({className:a,...b},c)=>(0,d.jsx)("h3",{ref:c,className:(0,h.cn)("text-2xl font-semibold leading-none tracking-tight",a),...b}));k.displayName="CardTitle";let l=g.forwardRef(({className:a,...b},c)=>(0,d.jsx)("p",{ref:c,className:(0,h.cn)("text-sm text-muted-foreground",a),...b}));l.displayName="CardDescription";let m=g.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,h.cn)("p-6 pt-0",a),...b}));m.displayName="CardContent",g.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,h.cn)("flex items-center p-6 pt-0",a),...b})).displayName="CardFooter";var n=c(75986);let o=a=>"boolean"==typeof a?`${a}`:0===a?"0":a,p=n.$,q=((a,b)=>c=>{var d;if((null==b?void 0:b.variants)==null)return p(a,null==c?void 0:c.class,null==c?void 0:c.className);let{variants:e,defaultVariants:f}=b,g=Object.keys(e).map(a=>{let b=null==c?void 0:c[a],d=null==f?void 0:f[a];if(null===b)return null;let g=o(b)||o(d);return e[a][g]}),h=c&&Object.entries(c).reduce((a,b)=>{let[c,d]=b;return void 0===d||(a[c]=d),a},{});return p(a,g,null==b||null==(d=b.compoundVariants)?void 0:d.reduce((a,b)=>{let{class:c,className:d,...e}=b;return Object.entries(e).every(a=>{let[b,c]=a;return Array.isArray(c)?c.includes({...f,...h}[b]):({...f,...h})[b]===c})?[...a,c,d]:a},[]),null==c?void 0:c.class,null==c?void 0:c.className)})("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),r=g.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...f},g)=>(0,d.jsx)("button",{className:(0,h.cn)(q({variant:b,size:c,className:a})),ref:g,...f}));r.displayName="Button";let s=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},t=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let v=(0,g.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:c=2,absoluteStrokeWidth:d,className:e="",children:f,iconNode:h,...i},j)=>(0,g.createElement)("svg",{ref:j,...u,width:b,height:b,stroke:a,strokeWidth:d?24*Number(c)/Number(b):c,className:t("lucide",e),...!f&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(i)&&{"aria-hidden":"true"},...i},[...h.map(([a,b])=>(0,g.createElement)(a,b)),...Array.isArray(f)?f:[f]])),w=(a,b)=>{let c=(0,g.forwardRef)(({className:c,...d},e)=>(0,g.createElement)(v,{ref:e,iconNode:b,className:t(`lucide-${s(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,c),...d}));return c.displayName=s(a),c},x=w("sparkles",[["path",{d:"M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z",key:"1s2grr"}],["path",{d:"M20 2v4",key:"1rf3ol"}],["path",{d:"M22 4h-4",key:"gwowj6"}],["circle",{cx:"4",cy:"20",r:"2",key:"6kqj1y"}]]),y=w("rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]]),z=w("bug",[["path",{d:"m8 2 1.88 1.88",key:"fmnt4t"}],["path",{d:"M14.12 3.88 16 2",key:"qol33r"}],["path",{d:"M9 7.13v-1a3.003 3.003 0 1 1 6 0v1",key:"d7y7pr"}],["path",{d:"M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6",key:"xs1cw7"}],["path",{d:"M12 20v-9",key:"1qisl0"}],["path",{d:"M6.53 9C4.6 8.8 3 7.1 3 5",key:"32zzws"}],["path",{d:"M6 13H2",key:"82j7cp"}],["path",{d:"M3 21c0-2.1 1.7-3.9 3.8-4",key:"4p0ekp"}],["path",{d:"M20.97 5c0 2.1-1.6 3.8-3.5 4",key:"18gb23"}],["path",{d:"M22 13h-4",key:"1jl80f"}],["path",{d:"M17.2 17c2.1.1 3.8 1.9 3.8 4",key:"k3fwyw"}]]),A=w("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),B=w("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),C=w("brain",[["path",{d:"M12 18V5",key:"adv99a"}],["path",{d:"M15 13a4.17 4.17 0 0 1-3-4 4.17 4.17 0 0 1-3 4",key:"1e3is1"}],["path",{d:"M17.598 6.5A3 3 0 1 0 12 5a3 3 0 1 0-5.598 1.5",key:"1gqd8o"}],["path",{d:"M17.997 5.125a4 4 0 0 1 2.526 5.77",key:"iwvgf7"}],["path",{d:"M18 18a4 4 0 0 0 2-7.464",key:"efp6ie"}],["path",{d:"M19.967 17.483A4 4 0 1 1 12 18a4 4 0 1 1-7.967-.517",key:"1gq6am"}],["path",{d:"M6 18a4 4 0 0 1-2-7.464",key:"k1g0md"}],["path",{d:"M6.003 5.125a4 4 0 0 0-2.526 5.77",key:"q97ue3"}]]),D=w("cpu",[["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M17 20v2",key:"1rnc9c"}],["path",{d:"M17 2v2",key:"11trls"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M2 17h2",key:"7oei6x"}],["path",{d:"M2 7h2",key:"asdhe0"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"M20 17h2",key:"1fpfkl"}],["path",{d:"M20 7h2",key:"1o8tra"}],["path",{d:"M7 20v2",key:"4gnj0m"}],["path",{d:"M7 2v2",key:"1i4yhu"}],["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"8",y:"8",width:"8",height:"8",rx:"1",key:"z9xiuo"}]]);function E(){return(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"relative overflow-hidden",children:(0,d.jsx)("div",{className:"container mx-auto px-4 py-20",children:(0,d.jsxs)("div",{className:"text-center mb-16",children:[(0,d.jsxs)("div",{className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#002857]/10 to-[#ff7514]/10 rounded-full mb-8 animate-bounce border border-[#ff7514]/20",children:[(0,d.jsx)(x,{className:"w-5 h-5 text-[#ff7514] mr-2"}),(0,d.jsx)("span",{className:"text-sm font-semibold text-[#002857]",children:"Nouvelle G\xe9n\xe9ration d'IA"}),(0,d.jsx)(y,{className:"w-5 h-5 text-[#ff7514] ml-2"})]}),(0,d.jsxs)("h1",{className:"text-6xl md:text-7xl font-black mb-6 leading-tight",children:[(0,d.jsx)("span",{className:"bg-gradient-to-r from-[#002857] via-[#003d7a] to-[#ff7514] bg-clip-text text-transparent",children:"Leoni Agents"}),(0,d.jsx)("br",{}),(0,d.jsx)("span",{className:"text-[#002857] text-4xl md:text-5xl font-bold",children:"Intelligence Artificielle"})]}),(0,d.jsxs)("p",{className:"text-xl md:text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed",children:["R\xe9volutionnez votre workflow avec nos agents IA de nouvelle g\xe9n\xe9ration.",(0,d.jsx)("span",{className:"text-[#ff7514] font-semibold",children:" Analyse d'erreurs intelligente"})," et",(0,d.jsx)("span",{className:"text-[#002857] font-semibold",children:" g\xe9n\xe9ration SQL automatis\xe9e"}),"pour une productivit\xe9 sans limites."]}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row justify-center gap-6 mb-16",children:[(0,d.jsx)(f(),{href:"/error-analysis",children:(0,d.jsxs)(r,{size:"lg",className:"group relative bg-gradient-to-r from-[#ff7514] to-[#ff8c42] hover:from-[#e6650f] hover:to-[#ff7514] text-white px-8 py-4 rounded-2xl shadow-xl shadow-[#ff7514]/25 transform hover:scale-105 transition-all duration-300",children:[(0,d.jsx)(z,{className:"w-6 h-6 mr-3 group-hover:animate-pulse"}),"Analyser les Erreurs",(0,d.jsx)(A,{className:"w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform"})]})}),(0,d.jsx)(f(),{href:"/sql-generation",children:(0,d.jsxs)(r,{size:"lg",variant:"outline",className:"group relative bg-white/90 backdrop-blur-sm border-2 border-[#002857]/30 hover:border-[#002857] text-[#002857] hover:text-[#001a3d] px-8 py-4 rounded-2xl shadow-xl hover:shadow-[#002857]/25 transform hover:scale-105 transition-all duration-300",children:[(0,d.jsx)(B,{className:"w-6 h-6 mr-3 group-hover:animate-pulse"}),"G\xe9n\xe9rer du SQL",(0,d.jsx)(A,{className:"w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform"})]})})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-[#ff7514] mb-2",children:"99.9%"}),(0,d.jsx)("div",{className:"text-gray-600",children:"Pr\xe9cision d'analyse"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-[#002857] mb-2",children:"<2s"}),(0,d.jsx)("div",{className:"text-gray-600",children:"Temps de r\xe9ponse"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-[#ff8c42] mb-2",children:"24/7"}),(0,d.jsx)("div",{className:"text-gray-600",children:"Disponibilit\xe9"})]})]})]})})}),(0,d.jsxs)("div",{className:"container mx-auto px-4 py-16",children:[(0,d.jsxs)("div",{className:"text-center mb-16",children:[(0,d.jsxs)("h2",{className:"text-4xl font-bold text-[#002857] mb-4",children:["Agents ",(0,d.jsx)("span",{className:"text-[#ff7514]",children:"Sp\xe9cialis\xe9s"})]}),(0,d.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"D\xe9couvrez nos agents IA de pointe, con\xe7us pour transformer votre fa\xe7on de travailler"})]}),(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-8 mb-16",children:[(0,d.jsxs)(i,{className:"group relative overflow-hidden bg-gradient-to-br from-[#ff7514]/5 to-[#ff7514]/10 border-0 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-[#ff7514]/10 to-[#ff8c42]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),(0,d.jsxs)(j,{className:"relative z-10",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("div",{className:"p-3 bg-gradient-to-r from-[#ff7514] to-[#ff8c42] rounded-2xl shadow-lg shadow-[#ff7514]/30",children:(0,d.jsx)(z,{className:"w-8 h-8 text-white"})}),(0,d.jsx)("div",{className:"px-3 py-1 bg-[#ff7514]/10 text-[#e6650f] rounded-full text-xs font-semibold",children:"EXPERT"})]}),(0,d.jsx)(k,{className:"text-2xl font-bold text-[#002857] mb-2",children:"Agent d'Analyse d'Erreurs"}),(0,d.jsx)(l,{className:"text-gray-600 text-base",children:"IA avanc\xe9e pour la d\xe9tection et r\xe9solution intelligente des erreurs de programme"})]}),(0,d.jsxs)(m,{className:"relative z-10",children:[(0,d.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,d.jsxs)("div",{className:"flex items-center text-gray-700",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-[#ff7514] rounded-full mr-3"}),(0,d.jsx)("span",{children:"Analyse automatique des fichiers d'erreur"})]}),(0,d.jsxs)("div",{className:"flex items-center text-gray-700",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-[#ff7514] rounded-full mr-3"}),(0,d.jsx)("span",{children:"Localisation pr\xe9cise avec num\xe9ros de ligne"})]}),(0,d.jsxs)("div",{className:"flex items-center text-gray-700",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-[#ff7514] rounded-full mr-3"}),(0,d.jsx)("span",{children:"Solutions d\xe9taill\xe9es et contexte de code"})]}),(0,d.jsxs)("div",{className:"flex items-center text-gray-700",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-[#ff7514] rounded-full mr-3"}),(0,d.jsx)("span",{children:"Support multi-langages de programmation"})]})]}),(0,d.jsx)(f(),{href:"/error-analysis",children:(0,d.jsxs)(r,{className:"w-full bg-gradient-to-r from-[#ff7514] to-[#ff8c42] hover:from-[#e6650f] hover:to-[#ff7514] text-white py-3 rounded-xl shadow-lg group-hover:shadow-[#ff7514]/25 transition-all duration-300",children:[(0,d.jsx)(C,{className:"w-5 h-5 mr-2"}),"Commencer l'Analyse",(0,d.jsx)(A,{className:"w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform"})]})})]})]}),(0,d.jsxs)(i,{className:"group relative overflow-hidden bg-gradient-to-br from-[#002857]/5 to-[#002857]/10 border-0 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-[#002857]/10 to-[#003d7a]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),(0,d.jsxs)(j,{className:"relative z-10",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("div",{className:"p-3 bg-gradient-to-r from-[#002857] to-[#003d7a] rounded-2xl shadow-lg shadow-[#002857]/30",children:(0,d.jsx)(B,{className:"w-8 h-8 text-white"})}),(0,d.jsx)("div",{className:"px-3 py-1 bg-[#002857]/10 text-[#001a3d] rounded-full text-xs font-semibold",children:"INNOVANT"})]}),(0,d.jsx)(k,{className:"text-2xl font-bold text-[#002857] mb-2",children:"Agent de G\xe9n\xe9ration SQL"}),(0,d.jsx)(l,{className:"text-gray-600 text-base",children:"Cr\xe9ation automatique de scripts SQL optimis\xe9s \xe0 partir de sp\xe9cifications"})]}),(0,d.jsxs)(m,{className:"relative z-10",children:[(0,d.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,d.jsxs)("div",{className:"flex items-center text-gray-700",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-[#002857] rounded-full mr-3"}),(0,d.jsx)("span",{children:"G\xe9n\xe9ration automatique de scripts SQL"})]}),(0,d.jsxs)("div",{className:"flex items-center text-gray-700",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-[#002857] rounded-full mr-3"}),(0,d.jsx)("span",{children:"Support multi-bases de donn\xe9es"})]}),(0,d.jsxs)("div",{className:"flex items-center text-gray-700",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-[#002857] rounded-full mr-3"}),(0,d.jsx)("span",{children:"Upload de fichiers de sp\xe9cification"})]}),(0,d.jsxs)("div",{className:"flex items-center text-gray-700",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-[#002857] rounded-full mr-3"}),(0,d.jsx)("span",{children:"Optimisation et bonnes pratiques"})]})]}),(0,d.jsx)(f(),{href:"/sql-generation",children:(0,d.jsxs)(r,{className:"w-full bg-gradient-to-r from-[#002857] to-[#003d7a] hover:from-[#001a3d] hover:to-[#002857] text-white py-3 rounded-xl shadow-lg group-hover:shadow-[#002857]/25 transition-all duration-300",children:[(0,d.jsx)(D,{className:"w-5 h-5 mr-2"}),"G\xe9n\xe9rer du SQL",(0,d.jsx)(A,{className:"w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform"})]})})]})]})]})]})]})}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},53679:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4536,23))},56657:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>B.default,__next_app__:()=>H,handler:()=>J,pages:()=>G,routeModule:()=>I,tree:()=>F});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(31369),C=c(30893),D=c(52836),E={};for(let a in C)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(E[a]=()=>C[a]);c.d(b,E);let F={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,14484)),"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\page.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,54431)),"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(c.bind(c,67393)),"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\loading.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,31369)),"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,31369)),"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\global-error.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,G=["C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\page.tsx"],H={require:c,loadChunk:()=>Promise.resolve()},I=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:F},distDir:".next",projectDir:""});async function J(a,b,c){var d;let E="/page";"/index"===E&&(E="/");let K="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await I.prepare(a,b,{srcPage:E,multiZoneDraftMode:K});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(E),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===I.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&I.isDev&&(az=_);let aA={...C,tree:F,pages:G,GlobalError:B.default,handler:J,routeModule:I,__next_app__:H};W&&X&&(0,n.setReferenceManifestsSingleton)({page:E,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return I.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:I,page:E,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:I.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:K,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>I.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:I.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!I.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===I.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await I.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await I.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),I.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!I.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&D.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await I.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},61927:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,85814,23))},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[985,565,17,748],()=>b(b.s=56657));module.exports=c})();