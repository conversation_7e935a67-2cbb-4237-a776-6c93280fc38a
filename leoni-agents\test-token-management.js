/**
 * Script de test rapide pour vérifier la gestion des tokens
 * Usage: node test-token-management.js
 */

// Simulation des fonctions utilitaires (version simplifiée pour test)
const CHARS_PER_TOKEN = 4;
const MAX_TOKENS = 5000;

function estimateTokens(text) {
  return Math.ceil(text.length / CHARS_PER_TOKEN);
}

function exceedsTokenLimit(text) {
  return estimateTokens(text) > MAX_TOKENS;
}

function validatePromptTokens(prompt, maxTokens = 8000) {
  const estimatedTokens = estimateTokens(prompt);
  const isValid = estimatedTokens <= maxTokens;
  const exceedsBy = Math.max(0, estimatedTokens - maxTokens);

  let recommendation = '';
  if (!isValid) {
    if (exceedsBy < 1000) {
      recommendation = 'Réduire légèrement le contenu ou utiliser des résumés plus courts';
    } else if (exceedsBy < 3000) {
      recommendation = 'Diviser le prompt en plusieurs parties ou tronquer significativement';
    } else {
      recommendation = 'Restructurer complètement l\'approche - contenu trop volumineux';
    }
  } else {
    recommendation = 'Prompt dans les limites acceptables';
  }

  return {
    isValid,
    estimatedTokens,
    exceedsBy,
    recommendation
  };
}

// Tests
console.log('🧪 Test de la gestion des tokens\n');

// Test 1: Texte court
console.log('📝 Test 1: Texte court');
const shortText = 'Hello world, this is a test';
console.log(`Texte: "${shortText}"`);
console.log(`Tokens estimés: ${estimateTokens(shortText)}`);
console.log(`Dépasse la limite: ${exceedsTokenLimit(shortText)}`);
console.log('');

// Test 2: Texte long
console.log('📝 Test 2: Texte long');
const longText = 'A'.repeat(25000); // ~6250 tokens
console.log(`Texte: ${longText.length} caractères`);
console.log(`Tokens estimés: ${estimateTokens(longText)}`);
console.log(`Dépasse la limite: ${exceedsTokenLimit(longText)}`);
console.log('');

// Test 3: Simulation d'un prompt d'agent
console.log('📝 Test 3: Simulation prompt d\'agent');
const basePrompt = `Vous êtes un agent d'analyse d'erreurs spécialisé dans l'analyse de code C/C++.
Votre rôle est d'analyser les fichiers de programme et d'erreur pour identifier les problèmes,
leurs emplacements précis et proposer des solutions détaillées.`;

const programContent = `
#include <stdio.h>
#include <stdlib.h>

int main() {
    int x = 10;
    int y = 0;
    int result = x / y; // Division par zéro
    printf("Result: %d\\n", result);
    return 0;
}
`.repeat(100); // Répéter pour simuler un gros fichier

const errorContent = `
error.c:7:18: runtime error: division by zero
error.c:8:5: warning: variable 'result' may be used uninitialized
`;

const analysisData = {
  functions: ['main'],
  variables: ['x', 'y', 'result'],
  errors: [
    { line: 7, type: 'DIVISION_BY_ZERO', severity: 'HIGH' },
    { line: 8, type: 'UNINITIALIZED_VAR', severity: 'MEDIUM' }
  ]
};

const fullPrompt = `${basePrompt}

FICHIER PROGRAMME:
${programContent}

FICHIER D'ERREUR:
${errorContent}

ANALYSE STATIQUE:
${JSON.stringify(analysisData, null, 2)}

Analysez ces fichiers et fournissez une réponse JSON structurée...`;

const validation = validatePromptTokens(fullPrompt);
console.log(`Prompt complet: ${fullPrompt.length} caractères`);
console.log(`Tokens estimés: ${validation.estimatedTokens}`);
console.log(`Est valide: ${validation.isValid}`);
console.log(`Dépasse de: ${validation.exceedsBy} tokens`);
console.log(`Recommandation: ${validation.recommendation}`);
console.log('');

// Test 4: Optimisation du prompt
console.log('📝 Test 4: Optimisation du prompt');
const maxAllowedTokens = 6000;
const sections = [
  { content: `ERREURS PARSÉES:\n${JSON.stringify(analysisData.errors, null, 2)}`, priority: 1 },
  { content: `FICHIER D'ERREUR:\n${errorContent}`, priority: 2 },
  { content: `FICHIER PROGRAMME:\n${programContent}`, priority: 3 },
  { content: `ANALYSE STATIQUE:\n${JSON.stringify(analysisData, null, 2)}`, priority: 4 }
];

let optimizedPrompt = basePrompt;
let remainingTokens = maxAllowedTokens - estimateTokens(basePrompt);
let includedSections = 0;

console.log(`Budget initial: ${remainingTokens} tokens`);

sections.sort((a, b) => a.priority - b.priority);

for (const section of sections) {
  const sectionTokens = estimateTokens(section.content);
  console.log(`Section priorité ${section.priority}: ${sectionTokens} tokens`);
  
  if (sectionTokens <= remainingTokens) {
    optimizedPrompt += '\n\n' + section.content;
    remainingTokens -= sectionTokens;
    includedSections++;
    console.log(`  ✅ Incluse (reste ${remainingTokens} tokens)`);
  } else {
    console.log(`  ❌ Exclue (pas assez de tokens)`);
    if (section.priority <= 2) {
      // Tronquer les sections critiques
      const maxChars = remainingTokens * 4;
      if (maxChars > 100) {
        const truncated = section.content.substring(0, maxChars) + '\n... [TRONQUÉ] ...';
        optimizedPrompt += '\n\n' + truncated;
        console.log(`  ⚠️  Tronquée à ${maxChars} caractères`);
        remainingTokens = 0;
        includedSections++;
        break;
      }
    }
  }
}

const finalValidation = validatePromptTokens(optimizedPrompt);
console.log(`\nPrompt optimisé:`);
console.log(`Sections incluses: ${includedSections}/${sections.length}`);
console.log(`Tokens finaux: ${finalValidation.estimatedTokens}`);
console.log(`Est valide: ${finalValidation.isValid}`);
console.log(`Recommandation: ${finalValidation.recommendation}`);

// Résumé
console.log('\n🎯 Résumé des améliorations:');
console.log('✅ Estimation précise des tokens');
console.log('✅ Validation avant envoi à l\'API');
console.log('✅ Optimisation par priorité des sections');
console.log('✅ Troncature intelligente si nécessaire');
console.log('✅ Messages d\'erreur informatifs');

if (finalValidation.isValid) {
  console.log('\n🎉 Le prompt optimisé respecte les limites de tokens !');
} else {
  console.log('\n⚠️  Le prompt nécessite encore des optimisations.');
}
