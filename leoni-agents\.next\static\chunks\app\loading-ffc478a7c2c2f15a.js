(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[209],{3311:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("sparkles",[["path",{d:"M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z",key:"1s2grr"}],["path",{d:"M20 2v4",key:"1rf3ol"}],["path",{d:"M22 4h-4",key:"gwowj6"}],["circle",{cx:"4",cy:"20",r:"2",key:"6kqj1y"}]])},6140:(e,t,r)=>{Promise.resolve().then(r.bind(r,8460))},8460:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var a=r(5155),s=r(3311);function l(){return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r from-[#ff7514] to-[#ff8c42] rounded-full flex items-center justify-center mx-auto mb-6 animate-pulse",children:(0,a.jsx)(s.A,{className:"w-8 h-8 text-white animate-spin"})}),(0,a.jsx)("div",{className:"absolute inset-0 w-16 h-16 bg-gradient-to-r from-[#ff7514] to-[#ff8c42] rounded-full mx-auto animate-ping opacity-20"})]}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-[#002857] mb-2",children:"Leoni Agents"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Chargement en cours..."}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-[#ff7514] rounded-full animate-bounce"}),(0,a.jsx)("div",{className:"w-2 h-2 bg-[#ff7514] rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-[#ff7514] rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})})]})})}},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var a=r(2115);let s=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:n=2,absoluteStrokeWidth:c,className:o="",children:d,iconNode:u,...f}=e;return(0,a.createElement)("svg",{ref:t,...i,width:s,height:s,stroke:r,strokeWidth:c?24*Number(n)/Number(s):n,className:l("lucide",o),...!d&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(f)&&{"aria-hidden":"true"},...f},[...u.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),c=(e,t)=>{let r=(0,a.forwardRef)((r,i)=>{let{className:c,...o}=r;return(0,a.createElement)(n,{ref:i,iconNode:t,className:l("lucide-".concat(s(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),c),...o})});return r.displayName=s(e),r}}},e=>{e.O(0,[441,964,358],()=>e(e.s=6140)),_N_E=e.O()}]);