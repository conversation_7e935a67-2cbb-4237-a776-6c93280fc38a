/**
 * Utilitaires pour gérer les textes longs et la limitation de tokens
 */

// Estimation approximative : 1 token ≈ 4 caractères pour le français
const CHARS_PER_TOKEN = 4;
const MAX_TOKENS = 5000; // Marge de sécurité plus importante sous la limite de 8192
const MAX_CHARS = MAX_TOKENS * CHARS_PER_TOKEN;

/**
 * Divise un texte long en chunks plus petits
 */
export function splitTextIntoChunks(text: string, maxChars: number = MAX_CHARS): string[] {
  if (text.length <= maxChars) {
    return [text];
  }

  const chunks: string[] = [];
  let currentIndex = 0;

  while (currentIndex < text.length) {
    let endIndex = currentIndex + maxChars;
    
    // Si on n'est pas à la fin du texte, essayons de couper à un endroit logique
    if (endIndex < text.length) {
      // Chercher le dernier saut de ligne dans la zone de coupe
      const lastNewline = text.lastIndexOf('\n', endIndex);
      if (lastNewline > currentIndex) {
        endIndex = lastNewline;
      } else {
        // Sinon, chercher le dernier espace
        const lastSpace = text.lastIndexOf(' ', endIndex);
        if (lastSpace > currentIndex) {
          endIndex = lastSpace;
        }
      }
    }

    chunks.push(text.slice(currentIndex, endIndex).trim());
    currentIndex = endIndex + 1;
  }

  return chunks.filter(chunk => chunk.length > 0);
}

/**
 * Résume un texte long en gardant les éléments essentiels
 */
export function summarizeSpecification(text: string): string {
  const lines = text.split('\n');
  const importantLines: string[] = [];
  
  // Mots-clés importants pour les spécifications SQL
  const keywords = [
    'table', 'column', 'field', 'primary key', 'foreign key', 'index',
    'constraint', 'relationship', 'join', 'select', 'insert', 'update', 'delete',
    'create', 'alter', 'drop', 'database', 'schema', 'view', 'procedure',
    'function', 'trigger', 'unique', 'not null', 'default', 'auto_increment'
  ];

  for (const line of lines) {
    const lowerLine = line.toLowerCase();
    
    // Garder les lignes qui contiennent des mots-clés importants
    if (keywords.some(keyword => lowerLine.includes(keyword))) {
      importantLines.push(line);
    }
    // Garder les lignes courtes qui semblent être des titres ou des définitions
    else if (line.trim().length > 0 && line.trim().length < 100 && 
             (line.includes(':') || line.match(/^[A-Z]/) || line.includes('='))) {
      importantLines.push(line);
    }
  }

  // Si le résumé est encore trop long, prendre seulement les premières lignes importantes
  let summary = importantLines.join('\n');
  if (summary.length > MAX_CHARS * 0.6) {
    const maxLines = Math.floor(importantLines.length * 0.4);
    summary = importantLines.slice(0, maxLines).join('\n');
    summary += '\n\n[... Spécification tronquée pour respecter les limites de tokens ...]';
  }

  // Si toujours trop long, coupe brutalement
  if (summary.length > MAX_CHARS * 0.6) {
    summary = summary.slice(0, MAX_CHARS * 0.6) + '\n\n[... Texte tronqué ...]';
  }

  return summary || text.slice(0, MAX_CHARS * 0.6) + '\n\n[... Texte tronqué ...]';
}

/**
 * Estime le nombre de tokens dans un texte
 */
export function estimateTokens(text: string): number {
  return Math.ceil(text.length / CHARS_PER_TOKEN);
}

/**
 * Vérifie si un texte dépasse la limite de tokens
 */
export function exceedsTokenLimit(text: string): boolean {
  return estimateTokens(text) > MAX_TOKENS;
}

/**
 * Prépare une spécification pour l'API en gérant la longueur
 */
export function prepareSpecificationForAPI(specification: string): {
  processedText: string;
  wasTruncated: boolean;
  originalLength: number;
  processedLength: number;
  estimatedTokens: number;
} {
  const originalLength = specification.length;
  const originalTokens = estimateTokens(specification);

  let processedText = specification;
  let wasTruncated = false;

  if (exceedsTokenLimit(specification)) {
    processedText = summarizeSpecification(specification);
    wasTruncated = true;
  }

  return {
    processedText,
    wasTruncated,
    originalLength,
    processedLength: processedText.length,
    estimatedTokens: estimateTokens(processedText)
  };
}

/**
 * Surveille et optimise un prompt pour éviter le dépassement de tokens
 */
export function optimizePromptForTokenLimit(
  basePrompt: string,
  sections: Array<{ content: string; priority: number; required?: boolean }>,
  maxTokens: number = 6000
): {
  optimizedPrompt: string;
  includedSections: number;
  totalTokens: number;
  wasOptimized: boolean;
} {
  let remainingTokens = maxTokens - estimateTokens(basePrompt);
  let optimizedPrompt = basePrompt;
  let includedSections = 0;
  let wasOptimized = false;

  // Trier par priorité (1 = plus prioritaire)
  const sortedSections = [...sections].sort((a, b) => a.priority - b.priority);

  for (const section of sortedSections) {
    const sectionTokens = estimateTokens(section.content);

    if (sectionTokens <= remainingTokens) {
      // La section rentre entièrement
      optimizedPrompt += '\n\n' + section.content;
      remainingTokens -= sectionTokens;
      includedSections++;
    } else if (section.required || section.priority <= 2) {
      // Section critique : essayer de la tronquer
      const maxChars = Math.max(100, remainingTokens * 4);
      if (maxChars < section.content.length) {
        const truncatedContent = section.content.substring(0, maxChars) + '\n... [CONTENU TRONQUÉ POUR LIMITE DE TOKENS] ...';
        optimizedPrompt += '\n\n' + truncatedContent;
        wasOptimized = true;
        includedSections++;
        remainingTokens = 0;
        break;
      } else {
        optimizedPrompt += '\n\n' + section.content;
        remainingTokens -= sectionTokens;
        includedSections++;
      }
    } else {
      // Section non critique : ignorer si pas de place
      wasOptimized = true;
    }
  }

  return {
    optimizedPrompt,
    includedSections,
    totalTokens: estimateTokens(optimizedPrompt),
    wasOptimized
  };
}

/**
 * Valide qu'un prompt ne dépasse pas la limite de tokens avant envoi à l'API
 */
export function validatePromptTokens(prompt: string, maxTokens: number = 8000): {
  isValid: boolean;
  estimatedTokens: number;
  exceedsBy: number;
  recommendation: string;
} {
  const estimatedTokens = estimateTokens(prompt);
  const isValid = estimatedTokens <= maxTokens;
  const exceedsBy = Math.max(0, estimatedTokens - maxTokens);

  let recommendation = '';
  if (!isValid) {
    if (exceedsBy < 1000) {
      recommendation = 'Réduire légèrement le contenu ou utiliser des résumés plus courts';
    } else if (exceedsBy < 3000) {
      recommendation = 'Diviser le prompt en plusieurs parties ou tronquer significativement';
    } else {
      recommendation = 'Restructurer complètement l\'approche - contenu trop volumineux';
    }
  } else {
    recommendation = 'Prompt dans les limites acceptables';
  }

  return {
    isValid,
    estimatedTokens,
    exceedsBy,
    recommendation
  };
}
