/**
 * Tests pour la gestion des tokens et l'optimisation des prompts
 */

import { 
  estimateTokens, 
  exceedsTokenLimit, 
  validatePromptTokens, 
  optimizePromptForTokenLimit,
  splitTextIntoChunks 
} from '../lib/textUtils';

describe('Token Management', () => {
  
  describe('estimateTokens', () => {
    it('should estimate tokens correctly for short text', () => {
      const text = 'Hello world';
      const tokens = estimateTokens(text);
      expect(tokens).toBe(Math.ceil(text.length / 4));
    });

    it('should estimate tokens for longer text', () => {
      const text = 'A'.repeat(1000);
      const tokens = estimateTokens(text);
      expect(tokens).toBe(250); // 1000 / 4
    });
  });

  describe('exceedsTokenLimit', () => {
    it('should return false for text within limit', () => {
      const text = 'A'.repeat(1000); // ~250 tokens
      expect(exceedsTokenLimit(text)).toBe(false);
    });

    it('should return true for text exceeding limit', () => {
      const text = 'A'.repeat(25000); // ~6250 tokens (exceeds 5000 limit)
      expect(exceedsTokenLimit(text)).toBe(true);
    });
  });

  describe('validatePromptTokens', () => {
    it('should validate prompt within limits', () => {
      const prompt = 'A'.repeat(1000);
      const result = validatePromptTokens(prompt, 1000);
      
      expect(result.isValid).toBe(true);
      expect(result.exceedsBy).toBe(0);
      expect(result.recommendation).toContain('acceptables');
    });

    it('should detect prompt exceeding limits', () => {
      const prompt = 'A'.repeat(10000); // ~2500 tokens
      const result = validatePromptTokens(prompt, 1000); // limit 1000 tokens
      
      expect(result.isValid).toBe(false);
      expect(result.exceedsBy).toBeGreaterThan(0);
      expect(result.recommendation).toContain('Diviser');
    });
  });

  describe('optimizePromptForTokenLimit', () => {
    it('should include all sections when within limit', () => {
      const basePrompt = 'Base prompt';
      const sections = [
        { content: 'Section 1', priority: 1 },
        { content: 'Section 2', priority: 2 },
        { content: 'Section 3', priority: 3 }
      ];

      const result = optimizePromptForTokenLimit(basePrompt, sections, 1000);
      
      expect(result.includedSections).toBe(3);
      expect(result.wasOptimized).toBe(false);
      expect(result.optimizedPrompt).toContain('Section 1');
      expect(result.optimizedPrompt).toContain('Section 2');
      expect(result.optimizedPrompt).toContain('Section 3');
    });

    it('should prioritize sections when exceeding limit', () => {
      const basePrompt = 'Base prompt';
      const sections = [
        { content: 'A'.repeat(100), priority: 1 }, // High priority
        { content: 'B'.repeat(100), priority: 3 }, // Low priority
        { content: 'C'.repeat(100), priority: 2 }  // Medium priority
      ];

      const result = optimizePromptForTokenLimit(basePrompt, sections, 50); // Very low limit
      
      expect(result.wasOptimized).toBe(true);
      expect(result.optimizedPrompt).toContain('A'); // High priority should be included
    });

    it('should truncate required sections if necessary', () => {
      const basePrompt = 'Base';
      const sections = [
        { content: 'A'.repeat(1000), priority: 1, required: true }
      ];

      const result = optimizePromptForTokenLimit(basePrompt, sections, 100);
      
      expect(result.wasOptimized).toBe(true);
      expect(result.optimizedPrompt).toContain('TRONQUÉ');
    });
  });

  describe('splitTextIntoChunks', () => {
    it('should return single chunk for short text', () => {
      const text = 'Short text';
      const chunks = splitTextIntoChunks(text, 1000);
      
      expect(chunks).toHaveLength(1);
      expect(chunks[0]).toBe(text);
    });

    it('should split long text into multiple chunks', () => {
      const text = 'A'.repeat(1000);
      const chunks = splitTextIntoChunks(text, 300);
      
      expect(chunks.length).toBeGreaterThan(1);
      expect(chunks.every(chunk => chunk.length <= 300)).toBe(true);
    });

    it('should split at logical boundaries', () => {
      const text = 'Line 1\nLine 2\nLine 3\nLine 4\nLine 5';
      const chunks = splitTextIntoChunks(text, 15);
      
      // Should split at newlines when possible
      expect(chunks.some(chunk => chunk.endsWith('1'))).toBe(true);
    });
  });

  describe('Integration Test - Large Content', () => {
    it('should handle very large content without exceeding limits', () => {
      // Simulate a large program file
      const largeContent = `
        // Large program file
        ${Array.from({ length: 1000 }, (_, i) => `function func${i}() { return ${i}; }`).join('\n')}
      `;

      // Test that we can estimate tokens
      const tokens = estimateTokens(largeContent);
      expect(tokens).toBeGreaterThan(1000);

      // Test that we can detect if it exceeds limits
      const exceeds = exceedsTokenLimit(largeContent);
      expect(exceeds).toBe(true);

      // Test that we can split it into manageable chunks
      const chunks = splitTextIntoChunks(largeContent, 5000);
      expect(chunks.length).toBeGreaterThan(1);
      expect(chunks.every(chunk => !exceedsTokenLimit(chunk))).toBe(true);
    });
  });

});

// Test helper pour simuler l'agent d'analyse
describe('ErrorAnalysisAgent Token Management', () => {
  
  it('should build optimized prompt without exceeding limits', () => {
    const mockProgramFile = {
      name: 'test.c',
      content: 'A'.repeat(10000) // Large content
    };

    const mockErrorFile = {
      name: 'errors.txt',
      content: 'Error 1\nError 2\nError 3'
    };

    const mockStaticAnalysis = {
      functions: ['main', 'helper'],
      variables: ['var1', 'var2'],
      issues: ['issue1', 'issue2']
    };

    const mockEnrichedErrors = [
      { lineNumber: 10, type: 'SYNTAX_ERROR', message: 'Missing semicolon' },
      { lineNumber: 25, type: 'TYPE_ERROR', message: 'Type mismatch' }
    ];

    // Simulate building sections like the agent does
    const sections = [
      {
        content: `ERREURS PARSÉES:\n${JSON.stringify(mockEnrichedErrors, null, 2)}`,
        priority: 1,
        required: true
      },
      {
        content: `FICHIER D'ERREUR:\n${mockErrorFile.content}`,
        priority: 2,
        required: true
      },
      {
        content: `FICHIER PROGRAMME:\n${mockProgramFile.content}`,
        priority: 3
      },
      {
        content: `ANALYSE STATIQUE:\n${JSON.stringify(mockStaticAnalysis, null, 2)}`,
        priority: 4
      }
    ];

    const basePrompt = 'Vous êtes un agent d\'analyse d\'erreurs...';
    const result = optimizePromptForTokenLimit(basePrompt, sections, 6000);

    // Should not exceed token limits
    const validation = validatePromptTokens(result.optimizedPrompt, 8000);
    expect(validation.isValid).toBe(true);

    // Should include critical sections
    expect(result.optimizedPrompt).toContain('ERREURS PARSÉES');
    expect(result.optimizedPrompt).toContain('FICHIER D\'ERREUR');
  });

});
