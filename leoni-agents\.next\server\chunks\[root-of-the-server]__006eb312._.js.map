{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Leoni/leoni-agents/src/lib/openai.ts"], "sourcesContent": ["import OpenAI from 'openai';\n\nconst openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY || '********************************************************************************************************************************************************************',\n  dangerouslyAllowBrowser: true\n});\n\nexport default openai;\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEA,MAAM,SAAS,IAAI,wKAAA,CAAA,UAAM,CAAC;IACxB,QAAQ,QAAQ,GAAG,CAAC,cAAc,IAAI;IACtC,yBAAyB;AAC3B;uCAEe", "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Leoni/leoni-agents/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('fr-FR', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit'\n  }).format(date)\n}\n\nexport interface ParsedError {\n  timestamp: string;\n  level: string;\n  message: string;\n  task?: string;\n  lineNumber?: number;\n  errorType?: string;\n  severity?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';\n  context?: string;\n  fileName?: string;\n}\n\nexport function parseErrorFile(content: string): ParsedError[] {\n  const lines = content.split('\\n').filter(line => line.trim());\n  return lines.map((line, index) => {\n    // Pattern principal pour les logs CAOFORS\n    const caoMatch = line.match(/^(\\d{2}\\.\\d{2}\\.\\d{4} \\d{2}:\\d{2}:\\d{2})\\s+(\\w+)\\s+(.+)$/);\n\n    if (caoMatch) {\n      const [, timestamp, level, message] = caoMatch;\n      const taskMatch = message.match(/task \\[([^\\]]+)\\]/);\n\n      // Patterns étendus pour extraire les numéros de ligne\n      const linePatterns = [\n        /(?:line|ligne)[:\\s]+(\\d+)/i,                    // line: 123, ligne: 123\n        /\\[.*(?:line|ligne)[:\\s]+(\\d+)/i,                // [something line: 123]\n        /caofors\\.ec line: (\\d+)/,                       // caofors.ec line: 123\n        /at line (\\d+)/i,                                // at line 123\n        /error on line (\\d+)/i,                          // error on line 123\n        /(\\d+):\\d+:/,                                    // 123:45: (format file:line:col)\n        /line (\\d+) column \\d+/i,                        // line 123 column 45\n        /\\((\\d+),\\d+\\)/,                                 // (123,45) format\n        /:\\s*(\\d+)\\s*:/,                                 // : 123 :\n        /ligne\\s+(\\d+)/i,                                // ligne 123\n        /row\\s+(\\d+)/i,                                  // row 123\n        /position\\s+(\\d+)/i                              // position 123\n      ];\n\n      let lineNumber: number | undefined;\n      for (const pattern of linePatterns) {\n        const match = message.match(pattern);\n        if (match) {\n          lineNumber = parseInt(match[1]);\n          break;\n        }\n      }\n\n      // Extraction du nom de fichier\n      const filePatterns = [\n        /([a-zA-Z_][a-zA-Z0-9_]*\\.[a-zA-Z]+)/,          // filename.ext\n        /in file ([^\\s]+)/i,                             // in file filename\n        /file \"([^\"]+)\"/i,                               // file \"filename\"\n        /([^\\s]+\\.(?:c|cpp|h|hpp|ec|sql|js|ts|py|java|cs))/i // common extensions\n      ];\n\n      let fileName: string | undefined;\n      for (const pattern of filePatterns) {\n        const match = message.match(pattern);\n        if (match) {\n          fileName = match[1];\n          break;\n        }\n      }\n\n      // Détection du type d'erreur\n      const errorType = detectErrorType(message);\n\n      // Détection de la sévérité\n      const severity = detectSeverity(level, message);\n\n      return {\n        timestamp,\n        level,\n        message,\n        task: taskMatch ? taskMatch[1] : undefined,\n        lineNumber,\n        errorType,\n        severity,\n        fileName\n      };\n    }\n\n    // Patterns pour d'autres formats de logs\n    const genericPatterns = [\n      // Format standard: ERROR: message at line 123\n      /^(ERROR|WARNING|INFO|DEBUG):\\s*(.+?)(?:\\s+at\\s+line\\s+(\\d+))?$/i,\n      // Format avec timestamp ISO\n      /^(\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2})\\s+(\\w+)\\s+(.+)$/,\n      // Format simple: level message\n      /^(\\w+):\\s*(.+)$/\n    ];\n\n    for (const pattern of genericPatterns) {\n      const match = line.match(pattern);\n      if (match) {\n        const level = match[1] || match[2] || 'UNKNOWN';\n        const message = match[2] || match[3] || match[1] || line;\n        const lineNumber = match[3] ? parseInt(match[3]) : undefined;\n\n        return {\n          timestamp: match[1]?.includes('T') ? match[1] : '',\n          level,\n          message,\n          lineNumber,\n          errorType: detectErrorType(message),\n          severity: detectSeverity(level, message)\n        };\n      }\n    }\n\n    // Fallback pour les lignes non reconnues\n    return {\n      timestamp: '',\n      level: 'UNKNOWN',\n      message: line,\n      lineNumber: index + 1,\n      errorType: 'UNKNOWN',\n      severity: 'LOW'\n    };\n  });\n}\n\nfunction detectErrorType(message: string): string {\n  const errorPatterns = [\n    { pattern: /variable.*not.*(?:initialized|declared|defined)/i, type: 'VARIABLE_NOT_INITIALIZED' },\n    { pattern: /undefined.*variable/i, type: 'UNDEFINED_VARIABLE' },\n    { pattern: /syntax.*error/i, type: 'SYNTAX_ERROR' },\n    { pattern: /compilation.*error/i, type: 'COMPILATION_ERROR' },\n    { pattern: /runtime.*error/i, type: 'RUNTIME_ERROR' },\n    { pattern: /null.*pointer/i, type: 'NULL_POINTER' },\n    { pattern: /memory.*leak/i, type: 'MEMORY_LEAK' },\n    { pattern: /buffer.*overflow/i, type: 'BUFFER_OVERFLOW' },\n    { pattern: /division.*by.*zero/i, type: 'DIVISION_BY_ZERO' },\n    { pattern: /file.*not.*found/i, type: 'FILE_NOT_FOUND' },\n    { pattern: /permission.*denied/i, type: 'PERMISSION_DENIED' },\n    { pattern: /connection.*failed/i, type: 'CONNECTION_ERROR' },\n    { pattern: /timeout/i, type: 'TIMEOUT_ERROR' },\n    { pattern: /locked.*already.*runs/i, type: 'RESOURCE_LOCKED' },\n    { pattern: /sql.*error/i, type: 'SQL_ERROR' },\n    { pattern: /database.*error/i, type: 'DATABASE_ERROR' },\n    { pattern: /assertion.*failed/i, type: 'ASSERTION_FAILED' },\n    { pattern: /stack.*overflow/i, type: 'STACK_OVERFLOW' },\n    { pattern: /out.*of.*memory/i, type: 'OUT_OF_MEMORY' },\n    { pattern: /invalid.*argument/i, type: 'INVALID_ARGUMENT' },\n    { pattern: /type.*mismatch/i, type: 'TYPE_MISMATCH' }\n  ];\n\n  for (const { pattern, type } of errorPatterns) {\n    if (pattern.test(message)) {\n      return type;\n    }\n  }\n\n  return 'GENERAL_ERROR';\n}\n\nfunction detectSeverity(level: string, message: string): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {\n  // Sévérité basée sur le niveau\n  const levelSeverity: Record<string, 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'> = {\n    'DEBUG': 'LOW',\n    'INFO': 'LOW',\n    'WARNING': 'MEDIUM',\n    'WARN': 'MEDIUM',\n    'ERROR': 'HIGH',\n    'FATAL': 'CRITICAL',\n    'CRITICAL': 'CRITICAL'\n  };\n\n  let severity = levelSeverity[level.toUpperCase()] || 'MEDIUM';\n\n  // Ajustement basé sur le contenu du message\n  const criticalPatterns = [\n    /crash/i, /fatal/i, /critical/i, /system.*failure/i,\n    /memory.*corruption/i, /security.*breach/i\n  ];\n\n  const highPatterns = [\n    /error/i, /exception/i, /failed/i, /abort/i,\n    /null.*pointer/i, /buffer.*overflow/i, /stack.*overflow/i\n  ];\n\n  const lowPatterns = [\n    /warning/i, /info/i, /debug/i, /notice/i\n  ];\n\n  if (criticalPatterns.some(pattern => pattern.test(message))) {\n    severity = 'CRITICAL';\n  } else if (highPatterns.some(pattern => pattern.test(message))) {\n    severity = severity === 'LOW' ? 'HIGH' : severity;\n  } else if (lowPatterns.some(pattern => pattern.test(message))) {\n    severity = severity === 'HIGH' ? 'MEDIUM' : severity;\n  }\n\n  return severity;\n}\n\nexport interface CodeContext {\n  targetLine: string;\n  contextLines: Array<{ number: number; content: string; isTarget: boolean }>;\n  analysis?: {\n    variables: string[];\n    functions: string[];\n    potentialIssues: string[];\n    suggestions: string[];\n  };\n}\n\nexport function extractLineFromProgram(programContent: string, lineNumber: number, context: number = 2): CodeContext {\n  const lines = programContent.split('\\n');\n  const targetLine = lines[lineNumber - 1] || '';\n\n  const startLine = Math.max(0, lineNumber - context - 1);\n  const endLine = Math.min(lines.length, lineNumber + context);\n\n  const contextLines = [];\n  for (let i = startLine; i < endLine; i++) {\n    contextLines.push({\n      number: i + 1,\n      content: lines[i] || '',\n      isTarget: i === lineNumber - 1\n    });\n  }\n\n  // Analyse contextuelle avancée\n  const analysis = analyzeCodeContext(lines, lineNumber, context);\n\n  return {\n    targetLine,\n    contextLines,\n    analysis\n  };\n}\n\nexport function analyzeCodeContext(lines: string[], lineNumber: number, context: number = 5): {\n  variables: string[];\n  functions: string[];\n  potentialIssues: string[];\n  suggestions: string[];\n} {\n  const startLine = Math.max(0, lineNumber - context - 1);\n  const endLine = Math.min(lines.length, lineNumber + context);\n  const contextCode = lines.slice(startLine, endLine).join('\\n');\n  const targetLine = lines[lineNumber - 1] || '';\n\n  // Extraction des variables\n  const variables = extractVariables(contextCode);\n\n  // Extraction des fonctions\n  const functions = extractFunctions(contextCode);\n\n  // Détection des problèmes potentiels\n  const potentialIssues = detectPotentialIssues(targetLine, contextCode);\n\n  // Génération de suggestions\n  const suggestions = generateSuggestions(targetLine, contextCode, potentialIssues);\n\n  return {\n    variables,\n    functions,\n    potentialIssues,\n    suggestions\n  };\n}\n\nfunction extractVariables(code: string): string[] {\n  const variables = new Set<string>();\n\n  // Patterns pour différents langages\n  const patterns = [\n    // C/C++: type var = value; ou type var;\n    /(?:int|float|double|char|long|short|unsigned|signed|bool|void)\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n    // Variables avec déclaration explicite\n    /(?:var|let|const)\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n    // Assignations\n    /([a-zA-Z_][a-zA-Z0-9_]*)\\s*=/g,\n    // Paramètres de fonction\n    /\\(\\s*([a-zA-Z_][a-zA-Z0-9_]*(?:\\s*,\\s*[a-zA-Z_][a-zA-Z0-9_]*)*)\\s*\\)/g\n  ];\n\n  patterns.forEach(pattern => {\n    let match;\n    while ((match = pattern.exec(code)) !== null) {\n      if (match[1]) {\n        // Séparer les variables multiples (pour les paramètres)\n        match[1].split(',').forEach(v => {\n          const varName = v.trim().split(/\\s+/).pop();\n          if (varName && varName.length > 1) {\n            variables.add(varName);\n          }\n        });\n      }\n    }\n  });\n\n  return Array.from(variables);\n}\n\nfunction extractFunctions(code: string): string[] {\n  const functions = new Set<string>();\n\n  const patterns = [\n    // Définitions de fonction C/C++\n    /(?:int|float|double|char|long|short|unsigned|signed|bool|void)\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(/g,\n    // Appels de fonction\n    /([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(/g,\n    // Fonctions JavaScript/TypeScript\n    /function\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n    // Méthodes\n    /\\.([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(/g\n  ];\n\n  patterns.forEach(pattern => {\n    let match;\n    while ((match = pattern.exec(code)) !== null) {\n      if (match[1] && match[1].length > 1) {\n        functions.add(match[1]);\n      }\n    }\n  });\n\n  return Array.from(functions);\n}\n\nfunction detectPotentialIssues(targetLine: string, contextCode: string): string[] {\n  const issues: string[] = [];\n\n  // Vérifications communes\n  const checks = [\n    {\n      pattern: /([a-zA-Z_][a-zA-Z0-9_]*)\\s*=\\s*[^;]*$/,\n      message: \"Variable potentiellement non initialisée ou assignation incomplète\"\n    },\n    {\n      pattern: /\\*\\s*([a-zA-Z_][a-zA-Z0-9_]*)\\s*(?![=])/,\n      message: \"Déréférencement de pointeur - vérifier si le pointeur est NULL\"\n    },\n    {\n      pattern: /\\[\\s*([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\]/,\n      message: \"Accès tableau - vérifier les limites d'index\"\n    },\n    {\n      pattern: /\\/\\*.*\\*\\//,\n      message: \"Code commenté - peut indiquer du code problématique\"\n    },\n    {\n      pattern: /TODO|FIXME|HACK|BUG/i,\n      message: \"Commentaire indiquant un problème connu\"\n    },\n    {\n      pattern: /malloc|calloc|free/,\n      message: \"Gestion mémoire manuelle - vérifier les fuites mémoire\"\n    },\n    {\n      pattern: /strcpy|strcat|sprintf/,\n      message: \"Fonction potentiellement dangereuse - risque de buffer overflow\"\n    }\n  ];\n\n  checks.forEach(check => {\n    if (check.pattern.test(targetLine)) {\n      issues.push(check.message);\n    }\n  });\n\n  // Vérifications contextuelles\n  if (contextCode.includes('EXEC SQL') && !contextCode.includes('SQLCODE_ERROR')) {\n    issues.push(\"Requête SQL sans vérification d'erreur appropriée\");\n  }\n\n  if (targetLine.includes('=') && !targetLine.includes(';')) {\n    issues.push(\"Assignation sans point-virgule terminal\");\n  }\n\n  return issues;\n}\n\nfunction generateSuggestions(targetLine: string, contextCode: string, issues: string[]): string[] {\n  const suggestions: string[] = [];\n\n  // Suggestions basées sur les problèmes détectés\n  if (issues.some(issue => issue.includes('non initialisée'))) {\n    suggestions.push(\"Initialiser la variable avant utilisation\");\n    suggestions.push(\"Vérifier la déclaration de la variable\");\n  }\n\n  if (issues.some(issue => issue.includes('pointeur'))) {\n    suggestions.push(\"Ajouter une vérification NULL avant déréférencement\");\n    suggestions.push(\"Utiliser des pointeurs intelligents si possible\");\n  }\n\n  if (issues.some(issue => issue.includes('tableau'))) {\n    suggestions.push(\"Vérifier que l'index est dans les limites du tableau\");\n    suggestions.push(\"Utiliser des fonctions sécurisées pour l'accès aux tableaux\");\n  }\n\n  if (targetLine.includes('EXEC SQL')) {\n    suggestions.push(\"Ajouter SQLCODE_ERROR_PUTERRDB_RET après la requête SQL\");\n    suggestions.push(\"Vérifier le code de retour SQL\");\n  }\n\n  // Suggestions générales\n  if (targetLine.trim().length === 0) {\n    suggestions.push(\"Ligne vide - vérifier si du code manque\");\n  }\n\n  if (targetLine.includes('//') || targetLine.includes('/*')) {\n    suggestions.push(\"Code commenté - vérifier si c'est intentionnel\");\n  }\n\n  return suggestions;\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,uIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;QACR,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAcO,SAAS,eAAe,OAAe;IAC5C,MAAM,QAAQ,QAAQ,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI;IAC1D,OAAO,MAAM,GAAG,CAAC,CAAC,MAAM;QACtB,0CAA0C;QAC1C,MAAM,WAAW,KAAK,KAAK,CAAC;QAE5B,IAAI,UAAU;YACZ,MAAM,GAAG,WAAW,OAAO,QAAQ,GAAG;YACtC,MAAM,YAAY,QAAQ,KAAK,CAAC;YAEhC,sDAAsD;YACtD,MAAM,eAAe;gBACnB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA,oBAAiD,eAAe;aACjE;YAED,IAAI;YACJ,KAAK,MAAM,WAAW,aAAc;gBAClC,MAAM,QAAQ,QAAQ,KAAK,CAAC;gBAC5B,IAAI,OAAO;oBACT,aAAa,SAAS,KAAK,CAAC,EAAE;oBAC9B;gBACF;YACF;YAEA,+BAA+B;YAC/B,MAAM,eAAe;gBACnB;gBACA;gBACA;gBACA,qDAAqD,oBAAoB;aAC1E;YAED,IAAI;YACJ,KAAK,MAAM,WAAW,aAAc;gBAClC,MAAM,QAAQ,QAAQ,KAAK,CAAC;gBAC5B,IAAI,OAAO;oBACT,WAAW,KAAK,CAAC,EAAE;oBACnB;gBACF;YACF;YAEA,6BAA6B;YAC7B,MAAM,YAAY,gBAAgB;YAElC,2BAA2B;YAC3B,MAAM,WAAW,eAAe,OAAO;YAEvC,OAAO;gBACL;gBACA;gBACA;gBACA,MAAM,YAAY,SAAS,CAAC,EAAE,GAAG;gBACjC;gBACA;gBACA;gBACA;YACF;QACF;QAEA,yCAAyC;QACzC,MAAM,kBAAkB;YACtB,8CAA8C;YAC9C;YACA,4BAA4B;YAC5B;YACA,+BAA+B;YAC/B;SACD;QAED,KAAK,MAAM,WAAW,gBAAiB;YACrC,MAAM,QAAQ,KAAK,KAAK,CAAC;YACzB,IAAI,OAAO;gBACT,MAAM,QAAQ,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,IAAI;gBACtC,MAAM,UAAU,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,IAAI;gBACpD,MAAM,aAAa,KAAK,CAAC,EAAE,GAAG,SAAS,KAAK,CAAC,EAAE,IAAI;gBAEnD,OAAO;oBACL,WAAW,KAAK,CAAC,EAAE,EAAE,SAAS,OAAO,KAAK,CAAC,EAAE,GAAG;oBAChD;oBACA;oBACA;oBACA,WAAW,gBAAgB;oBAC3B,UAAU,eAAe,OAAO;gBAClC;YACF;QACF;QAEA,yCAAyC;QACzC,OAAO;YACL,WAAW;YACX,OAAO;YACP,SAAS;YACT,YAAY,QAAQ;YACpB,WAAW;YACX,UAAU;QACZ;IACF;AACF;AAEA,SAAS,gBAAgB,OAAe;IACtC,MAAM,gBAAgB;QACpB;YAAE,SAAS;YAAoD,MAAM;QAA2B;QAChG;YAAE,SAAS;YAAwB,MAAM;QAAqB;QAC9D;YAAE,SAAS;YAAkB,MAAM;QAAe;QAClD;YAAE,SAAS;YAAuB,MAAM;QAAoB;QAC5D;YAAE,SAAS;YAAmB,MAAM;QAAgB;QACpD;YAAE,SAAS;YAAkB,MAAM;QAAe;QAClD;YAAE,SAAS;YAAiB,MAAM;QAAc;QAChD;YAAE,SAAS;YAAqB,MAAM;QAAkB;QACxD;YAAE,SAAS;YAAuB,MAAM;QAAmB;QAC3D;YAAE,SAAS;YAAqB,MAAM;QAAiB;QACvD;YAAE,SAAS;YAAuB,MAAM;QAAoB;QAC5D;YAAE,SAAS;YAAuB,MAAM;QAAmB;QAC3D;YAAE,SAAS;YAAY,MAAM;QAAgB;QAC7C;YAAE,SAAS;YAA0B,MAAM;QAAkB;QAC7D;YAAE,SAAS;YAAe,MAAM;QAAY;QAC5C;YAAE,SAAS;YAAoB,MAAM;QAAiB;QACtD;YAAE,SAAS;YAAsB,MAAM;QAAmB;QAC1D;YAAE,SAAS;YAAoB,MAAM;QAAiB;QACtD;YAAE,SAAS;YAAoB,MAAM;QAAgB;QACrD;YAAE,SAAS;YAAsB,MAAM;QAAmB;QAC1D;YAAE,SAAS;YAAmB,MAAM;QAAgB;KACrD;IAED,KAAK,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,cAAe;QAC7C,IAAI,QAAQ,IAAI,CAAC,UAAU;YACzB,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,SAAS,eAAe,KAAa,EAAE,OAAe;IACpD,+BAA+B;IAC/B,MAAM,gBAAwE;QAC5E,SAAS;QACT,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,SAAS;QACT,SAAS;QACT,YAAY;IACd;IAEA,IAAI,WAAW,aAAa,CAAC,MAAM,WAAW,GAAG,IAAI;IAErD,4CAA4C;IAC5C,MAAM,mBAAmB;QACvB;QAAU;QAAU;QAAa;QACjC;QAAuB;KACxB;IAED,MAAM,eAAe;QACnB;QAAU;QAAc;QAAW;QACnC;QAAkB;QAAqB;KACxC;IAED,MAAM,cAAc;QAClB;QAAY;QAAS;QAAU;KAChC;IAED,IAAI,iBAAiB,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC,WAAW;QAC3D,WAAW;IACb,OAAO,IAAI,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC,WAAW;QAC9D,WAAW,aAAa,QAAQ,SAAS;IAC3C,OAAO,IAAI,YAAY,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC,WAAW;QAC7D,WAAW,aAAa,SAAS,WAAW;IAC9C;IAEA,OAAO;AACT;AAaO,SAAS,uBAAuB,cAAsB,EAAE,UAAkB,EAAE,UAAkB,CAAC;IACpG,MAAM,QAAQ,eAAe,KAAK,CAAC;IACnC,MAAM,aAAa,KAAK,CAAC,aAAa,EAAE,IAAI;IAE5C,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,aAAa,UAAU;IACrD,MAAM,UAAU,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,aAAa;IAEpD,MAAM,eAAe,EAAE;IACvB,IAAK,IAAI,IAAI,WAAW,IAAI,SAAS,IAAK;QACxC,aAAa,IAAI,CAAC;YAChB,QAAQ,IAAI;YACZ,SAAS,KAAK,CAAC,EAAE,IAAI;YACrB,UAAU,MAAM,aAAa;QAC/B;IACF;IAEA,+BAA+B;IAC/B,MAAM,WAAW,mBAAmB,OAAO,YAAY;IAEvD,OAAO;QACL;QACA;QACA;IACF;AACF;AAEO,SAAS,mBAAmB,KAAe,EAAE,UAAkB,EAAE,UAAkB,CAAC;IAMzF,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,aAAa,UAAU;IACrD,MAAM,UAAU,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,aAAa;IACpD,MAAM,cAAc,MAAM,KAAK,CAAC,WAAW,SAAS,IAAI,CAAC;IACzD,MAAM,aAAa,KAAK,CAAC,aAAa,EAAE,IAAI;IAE5C,2BAA2B;IAC3B,MAAM,YAAY,iBAAiB;IAEnC,2BAA2B;IAC3B,MAAM,YAAY,iBAAiB;IAEnC,qCAAqC;IACrC,MAAM,kBAAkB,sBAAsB,YAAY;IAE1D,4BAA4B;IAC5B,MAAM,cAAc,oBAAoB,YAAY,aAAa;IAEjE,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAEA,SAAS,iBAAiB,IAAY;IACpC,MAAM,YAAY,IAAI;IAEtB,oCAAoC;IACpC,MAAM,WAAW;QACf,wCAAwC;QACxC;QACA,uCAAuC;QACvC;QACA,eAAe;QACf;QACA,yBAAyB;QACzB;KACD;IAED,SAAS,OAAO,CAAC,CAAA;QACf,IAAI;QACJ,MAAO,CAAC,QAAQ,QAAQ,IAAI,CAAC,KAAK,MAAM,KAAM;YAC5C,IAAI,KAAK,CAAC,EAAE,EAAE;gBACZ,wDAAwD;gBACxD,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,OAAO,CAAC,CAAA;oBAC1B,MAAM,UAAU,EAAE,IAAI,GAAG,KAAK,CAAC,OAAO,GAAG;oBACzC,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;wBACjC,UAAU,GAAG,CAAC;oBAChB;gBACF;YACF;QACF;IACF;IAEA,OAAO,MAAM,IAAI,CAAC;AACpB;AAEA,SAAS,iBAAiB,IAAY;IACpC,MAAM,YAAY,IAAI;IAEtB,MAAM,WAAW;QACf,gCAAgC;QAChC;QACA,qBAAqB;QACrB;QACA,kCAAkC;QAClC;QACA,WAAW;QACX;KACD;IAED,SAAS,OAAO,CAAC,CAAA;QACf,IAAI;QACJ,MAAO,CAAC,QAAQ,QAAQ,IAAI,CAAC,KAAK,MAAM,KAAM;YAC5C,IAAI,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,GAAG;gBACnC,UAAU,GAAG,CAAC,KAAK,CAAC,EAAE;YACxB;QACF;IACF;IAEA,OAAO,MAAM,IAAI,CAAC;AACpB;AAEA,SAAS,sBAAsB,UAAkB,EAAE,WAAmB;IACpE,MAAM,SAAmB,EAAE;IAE3B,yBAAyB;IACzB,MAAM,SAAS;QACb;YACE,SAAS;YACT,SAAS;QACX;QACA;YACE,SAAS;YACT,SAAS;QACX;QACA;YACE,SAAS;YACT,SAAS;QACX;QACA;YACE,SAAS;YACT,SAAS;QACX;QACA;YACE,SAAS;YACT,SAAS;QACX;QACA;YACE,SAAS;YACT,SAAS;QACX;QACA;YACE,SAAS;YACT,SAAS;QACX;KACD;IAED,OAAO,OAAO,CAAC,CAAA;QACb,IAAI,MAAM,OAAO,CAAC,IAAI,CAAC,aAAa;YAClC,OAAO,IAAI,CAAC,MAAM,OAAO;QAC3B;IACF;IAEA,8BAA8B;IAC9B,IAAI,YAAY,QAAQ,CAAC,eAAe,CAAC,YAAY,QAAQ,CAAC,kBAAkB;QAC9E,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,WAAW,QAAQ,CAAC,QAAQ,CAAC,WAAW,QAAQ,CAAC,MAAM;QACzD,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;AACT;AAEA,SAAS,oBAAoB,UAAkB,EAAE,WAAmB,EAAE,MAAgB;IACpF,MAAM,cAAwB,EAAE;IAEhC,gDAAgD;IAChD,IAAI,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,QAAQ,CAAC,qBAAqB;QAC3D,YAAY,IAAI,CAAC;QACjB,YAAY,IAAI,CAAC;IACnB;IAEA,IAAI,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,QAAQ,CAAC,cAAc;QACpD,YAAY,IAAI,CAAC;QACjB,YAAY,IAAI,CAAC;IACnB;IAEA,IAAI,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,QAAQ,CAAC,aAAa;QACnD,YAAY,IAAI,CAAC;QACjB,YAAY,IAAI,CAAC;IACnB;IAEA,IAAI,WAAW,QAAQ,CAAC,aAAa;QACnC,YAAY,IAAI,CAAC;QACjB,YAAY,IAAI,CAAC;IACnB;IAEA,wBAAwB;IACxB,IAAI,WAAW,IAAI,GAAG,MAAM,KAAK,GAAG;QAClC,YAAY,IAAI,CAAC;IACnB;IAEA,IAAI,WAAW,QAAQ,CAAC,SAAS,WAAW,QAAQ,CAAC,OAAO;QAC1D,YAAY,IAAI,CAAC;IACnB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 512, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Leoni/leoni-agents/src/agents/errorAnalysisAgent.ts"], "sourcesContent": ["import openai from '@/lib/openai';\nimport { ErrorAnalysisResult, ProgramFile, Agent, AutoFixSuggestion } from '@/types';\nimport { parseErrorFile, extractLineFromProgram, ParsedError, analyzeCodeContext } from '@/lib/utils';\n\nexport const errorAnalysisAgent: Agent = {\n  id: 'error-analysis-agent',\n  name: 'Agent d\\'Analyse d\\'Erreurs',\n  description: 'Analyse les fichiers de programme et d\\'erreur pour détecter les erreurs, leurs emplacements et proposer des solutions',\n  role: 'Expert en analyse d\\'erreurs et débogage de programmes',\n  goal: 'Identifier, localiser et proposer des solutions pour les erreurs dans les programmes',\n  prompt: `Tu es un expert en analyse d'erreurs et débogage de programmes avec des capacités avancées d'analyse statique.\n  Ton rôle est d'analyser les fichiers de programme et les fichiers d'erreur pour :\n  1. Détecter et identifier les erreurs avec précision\n  2. Localiser exactement où elles se produisent (ligne, colonne si possible)\n  3. Analyser le contexte du code autour des erreurs\n  4. Expliquer les causes possibles avec des détails techniques\n  5. Proposer des solutions concrètes et détaillées\n  6. Suggérer des corrections automatiques quand c'est possible\n  7. Identifier les erreurs liées ou en cascade\n  8. Proposer des améliorations préventives\n\n  Utilise l'analyse contextuelle fournie (variables, fonctions, problèmes potentiels) pour enrichir tes diagnostics.\n  Sois très précis dans tes analyses et fournis des solutions pratiques et applicables.\n  Priorise les erreurs par ordre de sévérité et d'impact.`,\n  tools: ['file-analysis', 'error-parsing', 'solution-generation'],\n  utils: ['parseErrorFile', 'formatDate']\n};\n\nexport class ErrorAnalysisService {\n  private agent: Agent;\n\n  constructor() {\n    this.agent = errorAnalysisAgent;\n  }\n\n  async analyzeFiles(programFile: ProgramFile, errorFile: ProgramFile): Promise<ErrorAnalysisResult> {\n    try {\n      // Parse error file to extract structured error information\n      const parsedErrors = parseErrorFile(errorFile.content);\n\n      // Analyse statique préliminaire du code\n      const staticAnalysis = this.performStaticAnalysis(programFile.content);\n\n      // Limit program file content to avoid token limit but keep relevant sections\n      let programContent = this.optimizeContentForAnalysis(programFile.content, parsedErrors);\n\n      // Enrichir les erreurs parsées avec l'analyse contextuelle\n      const enrichedErrors = this.enrichErrorsWithContext(parsedErrors, programFile.content);\n\n      const prompt = `${this.agent.prompt}\n\nANALYSE STATIQUE DU PROGRAMME:\n${JSON.stringify(staticAnalysis, null, 2)}\n\nFICHIER PROGRAMME:\nNom: ${programFile.name}\nContenu (optimisé pour l'analyse):\n${programContent}\n\nFICHIER D'ERREUR:\nNom: ${errorFile.name}\nContenu:\n${errorFile.content}\n\nERREURS PARSÉES AVEC CONTEXTE:\n${JSON.stringify(enrichedErrors, null, 2)}\n\nAnalyse ces fichiers et fournis une réponse JSON structurée avec:\n{\n  \"summary\": \"Résumé général de l'analyse avec priorités\",\n  \"errors\": [\n    {\n      \"errorType\": \"Type d'erreur spécifique\",\n      \"location\": \"Emplacement précis avec contexte\",\n      \"description\": \"Description détaillée avec analyse technique\",\n      \"possibleCauses\": [\"cause1 avec explication\", \"cause2 avec explication\"],\n      \"solutions\": [\"solution1 détaillée\", \"solution2 avec étapes\"],\n      \"severity\": \"LOW|MEDIUM|HIGH|CRITICAL\",\n      \"lineNumber\": 123,\n      \"fileName\": \"nom_du_fichier.ext\",\n      \"autoFixSuggestions\": [\n        {\n          \"type\": \"REPLACE|INSERT|DELETE|WRAP\",\n          \"description\": \"Description de la correction\",\n          \"suggestedCode\": \"Code suggéré\",\n          \"confidence\": \"LOW|MEDIUM|HIGH\"\n        }\n      ],\n      \"relatedErrors\": [1, 2]\n    }\n  ],\n  \"recommendations\": [\n    \"recommandation1 avec justification\",\n    \"recommandation2 préventive\"\n  ],\n  \"preventiveMeasures\": [\n    \"mesure1 pour éviter les erreurs similaires\",\n    \"mesure2 d'amélioration du code\"\n  ]\n}\n\nINSTRUCTIONS SPÉCIALES:\n- Utilise l'analyse contextuelle fournie pour enrichir tes diagnostics\n- Priorise les erreurs par sévérité et impact\n- Propose des corrections automatiques quand c'est possible\n- Identifie les erreurs liées ou en cascade\n- Inclus des mesures préventives\n- Sois très précis sur les numéros de ligne et les emplacements`;\n\n      const response = await openai.chat.completions.create({\n        model: \"gpt-4\",\n        messages: [\n          {\n            role: \"system\",\n            content: this.agent.prompt\n          },\n          {\n            role: \"user\",\n            content: prompt\n          }\n        ],\n        temperature: 0.3,\n        max_tokens: 2000\n      });\n\n      const content = response.choices[0]?.message?.content;\n      if (!content) {\n        throw new Error('Aucune réponse reçue de l\\'API OpenAI');\n      }\n\n      // Try to parse JSON response\n      let analysisResult: ErrorAnalysisResult;\n      try {\n        const jsonMatch = content.match(/\\{[\\s\\S]*\\}/);\n        if (jsonMatch) {\n          analysisResult = JSON.parse(jsonMatch[0]);\n        } else {\n          // Fallback if no JSON found\n          analysisResult = {\n            summary: content,\n            errors: [],\n            recommendations: [],\n            timestamp: new Date().toISOString()\n          };\n        }\n      } catch (parseError) {\n        // Fallback parsing\n        analysisResult = {\n          summary: content,\n          errors: [],\n          recommendations: [],\n          timestamp: new Date().toISOString()\n        };\n      }\n\n      analysisResult.timestamp = new Date().toISOString();\n\n      // Enrichir les erreurs avec le contexte de code et les suggestions de correction\n      if (analysisResult.errors) {\n        analysisResult.errors = analysisResult.errors.map((error, index) => {\n          let enrichedError = { ...error };\n\n          if (error.lineNumber && error.lineNumber > 0) {\n            // Ajouter le contexte de code\n            const codeContext = extractLineFromProgram(programFile.content, error.lineNumber);\n            enrichedError.codeContext = codeContext;\n\n            // Générer des suggestions de correction automatique\n            const parsedError = enrichedErrors.find(pe => pe.lineNumber === error.lineNumber);\n            if (parsedError) {\n              enrichedError.autoFixSuggestions = this.generateAutoFixSuggestions(parsedError, programFile.content);\n            }\n          }\n\n          return enrichedError;\n        });\n\n        // Identifier les erreurs liées\n        analysisResult.errors = this.identifyRelatedErrors(analysisResult.errors);\n      }\n\n      return analysisResult;\n\n    } catch (error) {\n      console.error('Erreur lors de l\\'analyse:', error);\n      throw new Error(`Erreur lors de l'analyse des fichiers: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);\n    }\n  }\n\n  getAgentInfo(): Agent {\n    return this.agent;\n  }\n\n  private performStaticAnalysis(programContent: string): {\n    totalLines: number;\n    functions: string[];\n    variables: string[];\n    potentialIssues: string[];\n    complexity: 'LOW' | 'MEDIUM' | 'HIGH';\n  } {\n    const lines = programContent.split('\\n');\n    const totalLines = lines.length;\n\n    // Analyse globale du code\n    const functions = new Set<string>();\n    const variables = new Set<string>();\n    const potentialIssues: string[] = [];\n\n    // Patterns pour détecter les fonctions\n    const functionPatterns = [\n      /(?:int|float|double|char|long|short|unsigned|signed|bool|void)\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(/g,\n      /function\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g\n    ];\n\n    // Patterns pour détecter les variables\n    const variablePatterns = [\n      /(?:int|float|double|char|long|short|unsigned|signed|bool)\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n      /(?:var|let|const)\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g\n    ];\n\n    // Analyse ligne par ligne\n    lines.forEach((line, index) => {\n      // Détecter les fonctions\n      functionPatterns.forEach(pattern => {\n        let match;\n        while ((match = pattern.exec(line)) !== null) {\n          functions.add(match[1]);\n        }\n      });\n\n      // Détecter les variables\n      variablePatterns.forEach(pattern => {\n        let match;\n        while ((match = pattern.exec(line)) !== null) {\n          variables.add(match[1]);\n        }\n      });\n\n      // Détecter les problèmes potentiels\n      if (line.includes('malloc') && !line.includes('free')) {\n        potentialIssues.push(`Ligne ${index + 1}: Allocation mémoire sans libération visible`);\n      }\n      if (line.includes('strcpy') || line.includes('strcat')) {\n        potentialIssues.push(`Ligne ${index + 1}: Fonction potentiellement dangereuse détectée`);\n      }\n      if (line.includes('TODO') || line.includes('FIXME')) {\n        potentialIssues.push(`Ligne ${index + 1}: Code incomplet ou problématique`);\n      }\n    });\n\n    // Calculer la complexité\n    let complexity: 'LOW' | 'MEDIUM' | 'HIGH' = 'LOW';\n    if (totalLines > 1000 || functions.size > 50) {\n      complexity = 'HIGH';\n    } else if (totalLines > 500 || functions.size > 20) {\n      complexity = 'MEDIUM';\n    }\n\n    return {\n      totalLines,\n      functions: Array.from(functions),\n      variables: Array.from(variables),\n      potentialIssues,\n      complexity\n    };\n  }\n\n  private optimizeContentForAnalysis(programContent: string, parsedErrors: ParsedError[]): string {\n    const lines = programContent.split('\\n');\n    const totalLines = lines.length;\n\n    // Si le contenu est petit, le garder entier\n    if (programContent.length <= 8000) {\n      return programContent;\n    }\n\n    // Identifier les lignes importantes basées sur les erreurs\n    const importantLines = new Set<number>();\n    parsedErrors.forEach(error => {\n      if (error.lineNumber) {\n        // Ajouter la ligne d'erreur et son contexte\n        for (let i = Math.max(0, error.lineNumber - 10); i <= Math.min(totalLines - 1, error.lineNumber + 10); i++) {\n          importantLines.add(i);\n        }\n      }\n    });\n\n    // Ajouter le début et la fin du fichier\n    for (let i = 0; i < Math.min(50, totalLines); i++) {\n      importantLines.add(i);\n    }\n    for (let i = Math.max(0, totalLines - 50); i < totalLines; i++) {\n      importantLines.add(i);\n    }\n\n    // Construire le contenu optimisé\n    const sortedLines = Array.from(importantLines).sort((a, b) => a - b);\n    let optimizedContent = '';\n    let lastLine = -1;\n\n    sortedLines.forEach(lineIndex => {\n      if (lineIndex > lastLine + 1) {\n        optimizedContent += '\\n... [LIGNES OMISES] ...\\n';\n      }\n      optimizedContent += `${lineIndex + 1}: ${lines[lineIndex]}\\n`;\n      lastLine = lineIndex;\n    });\n\n    return optimizedContent;\n  }\n\n  private enrichErrorsWithContext(parsedErrors: ParsedError[], programContent: string): ParsedError[] {\n    return parsedErrors.map(error => {\n      if (error.lineNumber) {\n        const contextAnalysis = analyzeCodeContext(programContent.split('\\n'), error.lineNumber);\n        return {\n          ...error,\n          context: {\n            variables: contextAnalysis.variables,\n            functions: contextAnalysis.functions,\n            potentialIssues: contextAnalysis.potentialIssues,\n            suggestions: contextAnalysis.suggestions\n          }\n        };\n      }\n      return error;\n    });\n  }\n\n  private generateAutoFixSuggestions(error: ParsedError, programContent: string): AutoFixSuggestion[] {\n    const suggestions: AutoFixSuggestion[] = [];\n\n    if (!error.lineNumber) return suggestions;\n\n    const lines = programContent.split('\\n');\n    const targetLine = lines[error.lineNumber - 1];\n\n    // Suggestions basées sur le type d'erreur\n    switch (error.errorType) {\n      case 'VARIABLE_NOT_INITIALIZED':\n        if (targetLine.includes('=')) {\n          suggestions.push({\n            type: 'REPLACE',\n            description: 'Initialiser la variable à une valeur par défaut',\n            originalCode: targetLine,\n            suggestedCode: targetLine.replace(/([a-zA-Z_][a-zA-Z0-9_]*)\\s*;/, '$1 = 0;'),\n            confidence: 'MEDIUM',\n            lineNumber: error.lineNumber\n          });\n        }\n        break;\n\n      case 'RESOURCE_LOCKED':\n        suggestions.push({\n          type: 'INSERT',\n          description: 'Ajouter une vérification de verrou avant exécution',\n          suggestedCode: 'if (!is_task_running(task_name)) {',\n          confidence: 'HIGH',\n          lineNumber: error.lineNumber\n        });\n        break;\n\n      case 'SQL_ERROR':\n        if (targetLine.includes('EXEC SQL') && !targetLine.includes('SQLCODE_ERROR')) {\n          suggestions.push({\n            type: 'INSERT',\n            description: 'Ajouter la vérification d\\'erreur SQL',\n            suggestedCode: 'SQLCODE_ERROR_PUTERRDB_RET(__LINE__, \"LOG\", \"caofors.ec\", \"description\");',\n            confidence: 'HIGH',\n            lineNumber: error.lineNumber + 1\n          });\n        }\n        break;\n    }\n\n    return suggestions;\n  }\n\n  private identifyRelatedErrors(errors: any[]): any[] {\n    return errors.map((error, index) => {\n      const relatedErrors: number[] = [];\n\n      // Chercher des erreurs sur des lignes proches\n      errors.forEach((otherError, otherIndex) => {\n        if (index !== otherIndex && error.lineNumber && otherError.lineNumber) {\n          const lineDiff = Math.abs(error.lineNumber - otherError.lineNumber);\n          if (lineDiff <= 5) {\n            relatedErrors.push(otherIndex);\n          }\n        }\n      });\n\n      // Chercher des erreurs du même type\n      errors.forEach((otherError, otherIndex) => {\n        if (index !== otherIndex && error.errorType === otherError.errorType) {\n          relatedErrors.push(otherIndex);\n        }\n      });\n\n      return {\n        ...error,\n        relatedErrors: [...new Set(relatedErrors)] // Supprimer les doublons\n      };\n    });\n  }\n\n  // Nouvelle méthode pour analyser un programme sans fichier d'erreur\n  async analyzeCodeOnly(programFile: ProgramFile): Promise<ErrorAnalysisResult> {\n    try {\n      // Analyse statique complète\n      const staticAnalysis = this.performStaticAnalysis(programFile.content);\n      const detectedIssues = this.detectStaticIssues(programFile.content);\n\n      const prompt = `${this.agent.prompt}\n\nANALYSE STATIQUE SEULE (sans fichier d'erreur):\n\nFICHIER PROGRAMME:\nNom: ${programFile.name}\nContenu:\n${programFile.content.length > 8000 ?\n  programFile.content.substring(0, 8000) + '\\n\\n... [CONTENU TRONQUÉ] ...' :\n  programFile.content}\n\nANALYSE STATIQUE:\n${JSON.stringify(staticAnalysis, null, 2)}\n\nPROBLÈMES DÉTECTÉS:\n${JSON.stringify(detectedIssues, null, 2)}\n\nEffectue une analyse statique complète et fournis une réponse JSON avec:\n{\n  \"summary\": \"Résumé de l'analyse statique avec évaluation de la qualité du code\",\n  \"errors\": [\n    {\n      \"errorType\": \"Type de problème détecté\",\n      \"location\": \"Emplacement dans le code\",\n      \"description\": \"Description du problème potentiel\",\n      \"possibleCauses\": [\"causes possibles\"],\n      \"solutions\": [\"solutions recommandées\"],\n      \"severity\": \"LOW|MEDIUM|HIGH|CRITICAL\",\n      \"lineNumber\": 123,\n      \"autoFixSuggestions\": [...]\n    }\n  ],\n  \"recommendations\": [\"améliorations générales du code\"],\n  \"codeQuality\": {\n    \"score\": 85,\n    \"strengths\": [\"points forts\"],\n    \"weaknesses\": [\"points faibles\"],\n    \"maintainability\": \"LOW|MEDIUM|HIGH\"\n  }\n}`;\n\n      const response = await openai.chat.completions.create({\n        model: \"gpt-4\",\n        messages: [\n          {\n            role: \"system\",\n            content: this.agent.prompt\n          },\n          {\n            role: \"user\",\n            content: prompt\n          }\n        ],\n        temperature: 0.2,\n        max_tokens: 3000\n      });\n\n      const content = response.choices[0]?.message?.content;\n      if (!content) {\n        throw new Error('Aucune réponse reçue de l\\'API OpenAI');\n      }\n\n      // Parse JSON response\n      let analysisResult: ErrorAnalysisResult;\n      try {\n        const jsonMatch = content.match(/\\{[\\s\\S]*\\}/);\n        if (jsonMatch) {\n          analysisResult = JSON.parse(jsonMatch[0]);\n        } else {\n          analysisResult = {\n            summary: content,\n            errors: [],\n            recommendations: [],\n            timestamp: new Date().toISOString()\n          };\n        }\n      } catch (parseError) {\n        analysisResult = {\n          summary: content,\n          errors: [],\n          recommendations: [],\n          timestamp: new Date().toISOString()\n        };\n      }\n\n      analysisResult.timestamp = new Date().toISOString();\n\n      // Enrichir avec le contexte de code\n      if (analysisResult.errors) {\n        analysisResult.errors = analysisResult.errors.map(error => {\n          if (error.lineNumber && error.lineNumber > 0) {\n            const codeContext = extractLineFromProgram(programFile.content, error.lineNumber);\n            return {\n              ...error,\n              codeContext\n            };\n          }\n          return error;\n        });\n      }\n\n      return analysisResult;\n\n    } catch (error) {\n      console.error('Erreur lors de l\\'analyse statique:', error);\n      throw new Error(`Erreur lors de l'analyse statique: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);\n    }\n  }\n\n  private detectStaticIssues(programContent: string): Array<{\n    type: string;\n    description: string;\n    lineNumber?: number;\n    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';\n  }> {\n    const issues: Array<{\n      type: string;\n      description: string;\n      lineNumber?: number;\n      severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';\n    }> = [];\n\n    const lines = programContent.split('\\n');\n\n    lines.forEach((line, index) => {\n      const lineNumber = index + 1;\n\n      // Détection de problèmes de sécurité\n      if (/strcpy|strcat|sprintf|gets/.test(line)) {\n        issues.push({\n          type: 'SECURITY_RISK',\n          description: 'Utilisation de fonction potentiellement dangereuse',\n          lineNumber,\n          severity: 'HIGH'\n        });\n      }\n\n      // Détection de fuites mémoire potentielles\n      if (/malloc|calloc/.test(line) && !programContent.includes('free')) {\n        issues.push({\n          type: 'MEMORY_LEAK',\n          description: 'Allocation mémoire sans libération visible',\n          lineNumber,\n          severity: 'MEDIUM'\n        });\n      }\n\n      // Détection de variables non initialisées\n      if (/(?:int|float|double|char)\\s+[a-zA-Z_][a-zA-Z0-9_]*\\s*;/.test(line)) {\n        issues.push({\n          type: 'UNINITIALIZED_VARIABLE',\n          description: 'Variable déclarée mais non initialisée',\n          lineNumber,\n          severity: 'MEDIUM'\n        });\n      }\n\n      // Détection de code mort\n      if (/\\/\\*.*\\*\\//.test(line) && line.includes('TODO')) {\n        issues.push({\n          type: 'DEAD_CODE',\n          description: 'Code commenté ou incomplet',\n          lineNumber,\n          severity: 'LOW'\n        });\n      }\n\n      // Détection de complexité excessive\n      if ((line.match(/if|while|for|switch/g) || []).length > 3) {\n        issues.push({\n          type: 'HIGH_COMPLEXITY',\n          description: 'Ligne avec complexité cyclomatique élevée',\n          lineNumber,\n          severity: 'LOW'\n        });\n      }\n\n      // Détection de SQL sans vérification d'erreur\n      if (/EXEC SQL/.test(line) && !lines[index + 1]?.includes('SQLCODE_ERROR')) {\n        issues.push({\n          type: 'SQL_NO_ERROR_CHECK',\n          description: 'Requête SQL sans vérification d\\'erreur',\n          lineNumber,\n          severity: 'HIGH'\n        });\n      }\n    });\n\n    return issues;\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;AAEO,MAAM,qBAA4B;IACvC,IAAI;IACJ,MAAM;IACN,aAAa;IACb,MAAM;IACN,MAAM;IACN,QAAQ,CAAC;;;;;;;;;;;;;yDAa8C,CAAC;IACxD,OAAO;QAAC;QAAiB;QAAiB;KAAsB;IAChE,OAAO;QAAC;QAAkB;KAAa;AACzC;AAEO,MAAM;IACH,MAAa;IAErB,aAAc;QACZ,IAAI,CAAC,KAAK,GAAG;IACf;IAEA,MAAM,aAAa,WAAwB,EAAE,SAAsB,EAAgC;QACjG,IAAI;YACF,2DAA2D;YAC3D,MAAM,eAAe,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,OAAO;YAErD,wCAAwC;YACxC,MAAM,iBAAiB,IAAI,CAAC,qBAAqB,CAAC,YAAY,OAAO;YAErE,6EAA6E;YAC7E,IAAI,iBAAiB,IAAI,CAAC,0BAA0B,CAAC,YAAY,OAAO,EAAE;YAE1E,2DAA2D;YAC3D,MAAM,iBAAiB,IAAI,CAAC,uBAAuB,CAAC,cAAc,YAAY,OAAO;YAErF,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;;;AAG1C,EAAE,KAAK,SAAS,CAAC,gBAAgB,MAAM,GAAG;;;KAGrC,EAAE,YAAY,IAAI,CAAC;;AAExB,EAAE,eAAe;;;KAGZ,EAAE,UAAU,IAAI,CAAC;;AAEtB,EAAE,UAAU,OAAO,CAAC;;;AAGpB,EAAE,KAAK,SAAS,CAAC,gBAAgB,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+DA0CqB,CAAC;YAE1D,MAAM,WAAW,MAAM,sHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACpD,OAAO;gBACP,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM;oBAC5B;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,YAAY;YACd;YAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;YAC9C,IAAI,CAAC,SAAS;gBACZ,MAAM,IAAI,MAAM;YAClB;YAEA,6BAA6B;YAC7B,IAAI;YACJ,IAAI;gBACF,MAAM,YAAY,QAAQ,KAAK,CAAC;gBAChC,IAAI,WAAW;oBACb,iBAAiB,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;gBAC1C,OAAO;oBACL,4BAA4B;oBAC5B,iBAAiB;wBACf,SAAS;wBACT,QAAQ,EAAE;wBACV,iBAAiB,EAAE;wBACnB,WAAW,IAAI,OAAO,WAAW;oBACnC;gBACF;YACF,EAAE,OAAO,YAAY;gBACnB,mBAAmB;gBACnB,iBAAiB;oBACf,SAAS;oBACT,QAAQ,EAAE;oBACV,iBAAiB,EAAE;oBACnB,WAAW,IAAI,OAAO,WAAW;gBACnC;YACF;YAEA,eAAe,SAAS,GAAG,IAAI,OAAO,WAAW;YAEjD,iFAAiF;YACjF,IAAI,eAAe,MAAM,EAAE;gBACzB,eAAe,MAAM,GAAG,eAAe,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO;oBACxD,IAAI,gBAAgB;wBAAE,GAAG,KAAK;oBAAC;oBAE/B,IAAI,MAAM,UAAU,IAAI,MAAM,UAAU,GAAG,GAAG;wBAC5C,8BAA8B;wBAC9B,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,yBAAsB,AAAD,EAAE,YAAY,OAAO,EAAE,MAAM,UAAU;wBAChF,cAAc,WAAW,GAAG;wBAE5B,oDAAoD;wBACpD,MAAM,cAAc,eAAe,IAAI,CAAC,CAAA,KAAM,GAAG,UAAU,KAAK,MAAM,UAAU;wBAChF,IAAI,aAAa;4BACf,cAAc,kBAAkB,GAAG,IAAI,CAAC,0BAA0B,CAAC,aAAa,YAAY,OAAO;wBACrG;oBACF;oBAEA,OAAO;gBACT;gBAEA,+BAA+B;gBAC/B,eAAe,MAAM,GAAG,IAAI,CAAC,qBAAqB,CAAC,eAAe,MAAM;YAC1E;YAEA,OAAO;QAET,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM,IAAI,MAAM,CAAC,uCAAuC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,mBAAmB;QACxH;IACF;IAEA,eAAsB;QACpB,OAAO,IAAI,CAAC,KAAK;IACnB;IAEQ,sBAAsB,cAAsB,EAMlD;QACA,MAAM,QAAQ,eAAe,KAAK,CAAC;QACnC,MAAM,aAAa,MAAM,MAAM;QAE/B,0BAA0B;QAC1B,MAAM,YAAY,IAAI;QACtB,MAAM,YAAY,IAAI;QACtB,MAAM,kBAA4B,EAAE;QAEpC,uCAAuC;QACvC,MAAM,mBAAmB;YACvB;YACA;SACD;QAED,uCAAuC;QACvC,MAAM,mBAAmB;YACvB;YACA;SACD;QAED,0BAA0B;QAC1B,MAAM,OAAO,CAAC,CAAC,MAAM;YACnB,yBAAyB;YACzB,iBAAiB,OAAO,CAAC,CAAA;gBACvB,IAAI;gBACJ,MAAO,CAAC,QAAQ,QAAQ,IAAI,CAAC,KAAK,MAAM,KAAM;oBAC5C,UAAU,GAAG,CAAC,KAAK,CAAC,EAAE;gBACxB;YACF;YAEA,yBAAyB;YACzB,iBAAiB,OAAO,CAAC,CAAA;gBACvB,IAAI;gBACJ,MAAO,CAAC,QAAQ,QAAQ,IAAI,CAAC,KAAK,MAAM,KAAM;oBAC5C,UAAU,GAAG,CAAC,KAAK,CAAC,EAAE;gBACxB;YACF;YAEA,oCAAoC;YACpC,IAAI,KAAK,QAAQ,CAAC,aAAa,CAAC,KAAK,QAAQ,CAAC,SAAS;gBACrD,gBAAgB,IAAI,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,4CAA4C,CAAC;YACvF;YACA,IAAI,KAAK,QAAQ,CAAC,aAAa,KAAK,QAAQ,CAAC,WAAW;gBACtD,gBAAgB,IAAI,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,8CAA8C,CAAC;YACzF;YACA,IAAI,KAAK,QAAQ,CAAC,WAAW,KAAK,QAAQ,CAAC,UAAU;gBACnD,gBAAgB,IAAI,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,iCAAiC,CAAC;YAC5E;QACF;QAEA,yBAAyB;QACzB,IAAI,aAAwC;QAC5C,IAAI,aAAa,QAAQ,UAAU,IAAI,GAAG,IAAI;YAC5C,aAAa;QACf,OAAO,IAAI,aAAa,OAAO,UAAU,IAAI,GAAG,IAAI;YAClD,aAAa;QACf;QAEA,OAAO;YACL;YACA,WAAW,MAAM,IAAI,CAAC;YACtB,WAAW,MAAM,IAAI,CAAC;YACtB;YACA;QACF;IACF;IAEQ,2BAA2B,cAAsB,EAAE,YAA2B,EAAU;QAC9F,MAAM,QAAQ,eAAe,KAAK,CAAC;QACnC,MAAM,aAAa,MAAM,MAAM;QAE/B,4CAA4C;QAC5C,IAAI,eAAe,MAAM,IAAI,MAAM;YACjC,OAAO;QACT;QAEA,2DAA2D;QAC3D,MAAM,iBAAiB,IAAI;QAC3B,aAAa,OAAO,CAAC,CAAA;YACnB,IAAI,MAAM,UAAU,EAAE;gBACpB,4CAA4C;gBAC5C,IAAK,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,MAAM,UAAU,GAAG,KAAK,KAAK,KAAK,GAAG,CAAC,aAAa,GAAG,MAAM,UAAU,GAAG,KAAK,IAAK;oBAC1G,eAAe,GAAG,CAAC;gBACrB;YACF;QACF;QAEA,wCAAwC;QACxC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,GAAG,CAAC,IAAI,aAAa,IAAK;YACjD,eAAe,GAAG,CAAC;QACrB;QACA,IAAK,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,aAAa,KAAK,IAAI,YAAY,IAAK;YAC9D,eAAe,GAAG,CAAC;QACrB;QAEA,iCAAiC;QACjC,MAAM,cAAc,MAAM,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;QAClE,IAAI,mBAAmB;QACvB,IAAI,WAAW,CAAC;QAEhB,YAAY,OAAO,CAAC,CAAA;YAClB,IAAI,YAAY,WAAW,GAAG;gBAC5B,oBAAoB;YACtB;YACA,oBAAoB,GAAG,YAAY,EAAE,EAAE,EAAE,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC;YAC7D,WAAW;QACb;QAEA,OAAO;IACT;IAEQ,wBAAwB,YAA2B,EAAE,cAAsB,EAAiB;QAClG,OAAO,aAAa,GAAG,CAAC,CAAA;YACtB,IAAI,MAAM,UAAU,EAAE;gBACpB,MAAM,kBAAkB,CAAA,GAAA,qHAAA,CAAA,qBAAkB,AAAD,EAAE,eAAe,KAAK,CAAC,OAAO,MAAM,UAAU;gBACvF,OAAO;oBACL,GAAG,KAAK;oBACR,SAAS;wBACP,WAAW,gBAAgB,SAAS;wBACpC,WAAW,gBAAgB,SAAS;wBACpC,iBAAiB,gBAAgB,eAAe;wBAChD,aAAa,gBAAgB,WAAW;oBAC1C;gBACF;YACF;YACA,OAAO;QACT;IACF;IAEQ,2BAA2B,KAAkB,EAAE,cAAsB,EAAuB;QAClG,MAAM,cAAmC,EAAE;QAE3C,IAAI,CAAC,MAAM,UAAU,EAAE,OAAO;QAE9B,MAAM,QAAQ,eAAe,KAAK,CAAC;QACnC,MAAM,aAAa,KAAK,CAAC,MAAM,UAAU,GAAG,EAAE;QAE9C,0CAA0C;QAC1C,OAAQ,MAAM,SAAS;YACrB,KAAK;gBACH,IAAI,WAAW,QAAQ,CAAC,MAAM;oBAC5B,YAAY,IAAI,CAAC;wBACf,MAAM;wBACN,aAAa;wBACb,cAAc;wBACd,eAAe,WAAW,OAAO,CAAC,gCAAgC;wBAClE,YAAY;wBACZ,YAAY,MAAM,UAAU;oBAC9B;gBACF;gBACA;YAEF,KAAK;gBACH,YAAY,IAAI,CAAC;oBACf,MAAM;oBACN,aAAa;oBACb,eAAe;oBACf,YAAY;oBACZ,YAAY,MAAM,UAAU;gBAC9B;gBACA;YAEF,KAAK;gBACH,IAAI,WAAW,QAAQ,CAAC,eAAe,CAAC,WAAW,QAAQ,CAAC,kBAAkB;oBAC5E,YAAY,IAAI,CAAC;wBACf,MAAM;wBACN,aAAa;wBACb,eAAe;wBACf,YAAY;wBACZ,YAAY,MAAM,UAAU,GAAG;oBACjC;gBACF;gBACA;QACJ;QAEA,OAAO;IACT;IAEQ,sBAAsB,MAAa,EAAS;QAClD,OAAO,OAAO,GAAG,CAAC,CAAC,OAAO;YACxB,MAAM,gBAA0B,EAAE;YAElC,8CAA8C;YAC9C,OAAO,OAAO,CAAC,CAAC,YAAY;gBAC1B,IAAI,UAAU,cAAc,MAAM,UAAU,IAAI,WAAW,UAAU,EAAE;oBACrE,MAAM,WAAW,KAAK,GAAG,CAAC,MAAM,UAAU,GAAG,WAAW,UAAU;oBAClE,IAAI,YAAY,GAAG;wBACjB,cAAc,IAAI,CAAC;oBACrB;gBACF;YACF;YAEA,oCAAoC;YACpC,OAAO,OAAO,CAAC,CAAC,YAAY;gBAC1B,IAAI,UAAU,cAAc,MAAM,SAAS,KAAK,WAAW,SAAS,EAAE;oBACpE,cAAc,IAAI,CAAC;gBACrB;YACF;YAEA,OAAO;gBACL,GAAG,KAAK;gBACR,eAAe;uBAAI,IAAI,IAAI;iBAAe,CAAC,yBAAyB;YACtE;QACF;IACF;IAEA,oEAAoE;IACpE,MAAM,gBAAgB,WAAwB,EAAgC;QAC5E,IAAI;YACF,4BAA4B;YAC5B,MAAM,iBAAiB,IAAI,CAAC,qBAAqB,CAAC,YAAY,OAAO;YACrE,MAAM,iBAAiB,IAAI,CAAC,kBAAkB,CAAC,YAAY,OAAO;YAElE,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;;;;;KAKrC,EAAE,YAAY,IAAI,CAAC;;AAExB,EAAE,YAAY,OAAO,CAAC,MAAM,GAAG,OAC7B,YAAY,OAAO,CAAC,SAAS,CAAC,GAAG,QAAQ,kCACzC,YAAY,OAAO,CAAC;;;AAGtB,EAAE,KAAK,SAAS,CAAC,gBAAgB,MAAM,GAAG;;;AAG1C,EAAE,KAAK,SAAS,CAAC,gBAAgB,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;CAwBzC,CAAC;YAEI,MAAM,WAAW,MAAM,sHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACpD,OAAO;gBACP,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM;oBAC5B;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,YAAY;YACd;YAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;YAC9C,IAAI,CAAC,SAAS;gBACZ,MAAM,IAAI,MAAM;YAClB;YAEA,sBAAsB;YACtB,IAAI;YACJ,IAAI;gBACF,MAAM,YAAY,QAAQ,KAAK,CAAC;gBAChC,IAAI,WAAW;oBACb,iBAAiB,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE;gBAC1C,OAAO;oBACL,iBAAiB;wBACf,SAAS;wBACT,QAAQ,EAAE;wBACV,iBAAiB,EAAE;wBACnB,WAAW,IAAI,OAAO,WAAW;oBACnC;gBACF;YACF,EAAE,OAAO,YAAY;gBACnB,iBAAiB;oBACf,SAAS;oBACT,QAAQ,EAAE;oBACV,iBAAiB,EAAE;oBACnB,WAAW,IAAI,OAAO,WAAW;gBACnC;YACF;YAEA,eAAe,SAAS,GAAG,IAAI,OAAO,WAAW;YAEjD,oCAAoC;YACpC,IAAI,eAAe,MAAM,EAAE;gBACzB,eAAe,MAAM,GAAG,eAAe,MAAM,CAAC,GAAG,CAAC,CAAA;oBAChD,IAAI,MAAM,UAAU,IAAI,MAAM,UAAU,GAAG,GAAG;wBAC5C,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,yBAAsB,AAAD,EAAE,YAAY,OAAO,EAAE,MAAM,UAAU;wBAChF,OAAO;4BACL,GAAG,KAAK;4BACR;wBACF;oBACF;oBACA,OAAO;gBACT;YACF;YAEA,OAAO;QAET,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,mBAAmB;QACpH;IACF;IAEQ,mBAAmB,cAAsB,EAK9C;QACD,MAAM,SAKD,EAAE;QAEP,MAAM,QAAQ,eAAe,KAAK,CAAC;QAEnC,MAAM,OAAO,CAAC,CAAC,MAAM;YACnB,MAAM,aAAa,QAAQ;YAE3B,qCAAqC;YACrC,IAAI,6BAA6B,IAAI,CAAC,OAAO;gBAC3C,OAAO,IAAI,CAAC;oBACV,MAAM;oBACN,aAAa;oBACb;oBACA,UAAU;gBACZ;YACF;YAEA,2CAA2C;YAC3C,IAAI,gBAAgB,IAAI,CAAC,SAAS,CAAC,eAAe,QAAQ,CAAC,SAAS;gBAClE,OAAO,IAAI,CAAC;oBACV,MAAM;oBACN,aAAa;oBACb;oBACA,UAAU;gBACZ;YACF;YAEA,0CAA0C;YAC1C,IAAI,yDAAyD,IAAI,CAAC,OAAO;gBACvE,OAAO,IAAI,CAAC;oBACV,MAAM;oBACN,aAAa;oBACb;oBACA,UAAU;gBACZ;YACF;YAEA,yBAAyB;YACzB,IAAI,aAAa,IAAI,CAAC,SAAS,KAAK,QAAQ,CAAC,SAAS;gBACpD,OAAO,IAAI,CAAC;oBACV,MAAM;oBACN,aAAa;oBACb;oBACA,UAAU;gBACZ;YACF;YAEA,oCAAoC;YACpC,IAAI,CAAC,KAAK,KAAK,CAAC,2BAA2B,EAAE,EAAE,MAAM,GAAG,GAAG;gBACzD,OAAO,IAAI,CAAC;oBACV,MAAM;oBACN,aAAa;oBACb;oBACA,UAAU;gBACZ;YACF;YAEA,8CAA8C;YAC9C,IAAI,WAAW,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,SAAS,kBAAkB;gBACzE,OAAO,IAAI,CAAC;oBACV,MAAM;oBACN,aAAa;oBACb;oBACA,UAAU;gBACZ;YACF;QACF;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1051, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Leoni/leoni-agents/src/app/api/error-analysis/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { ErrorAnalysisService } from '@/agents/errorAnalysisAgent';\nimport { ProgramFile } from '@/types';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { programFile, errorFile }: { programFile: ProgramFile; errorFile: ProgramFile } = body;\n\n    if (!programFile || !errorFile) {\n      return NextResponse.json(\n        { error: 'Les fichiers programme et erreur sont requis' },\n        { status: 400 }\n      );\n    }\n\n    if (!programFile.content || !errorFile.content) {\n      return NextResponse.json(\n        { error: 'Le contenu des fichiers ne peut pas être vide' },\n        { status: 400 }\n      );\n    }\n\n    const errorAnalysisService = new ErrorAnalysisService();\n    const result = await errorAnalysisService.analyzeFiles(programFile, errorFile);\n\n    return NextResponse.json({\n      success: true,\n      data: result,\n      timestamp: new Date().toISOString()\n    });\n\n  } catch (error) {\n    console.error('Erreur lors de l\\'analyse d\\'erreurs:', error);\n    \n    return NextResponse.json(\n      {\n        success: false,\n        error: error instanceof Error ? error.message : 'Erreur interne du serveur',\n        timestamp: new Date().toISOString()\n      },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function GET() {\n  const errorAnalysisService = new ErrorAnalysisService();\n  const agentInfo = errorAnalysisService.getAgentInfo();\n\n  return NextResponse.json({\n    success: true,\n    data: agentInfo,\n    timestamp: new Date().toISOString()\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAyD;QAEzF,IAAI,CAAC,eAAe,CAAC,WAAW;YAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+C,GACxD;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,YAAY,OAAO,IAAI,CAAC,UAAU,OAAO,EAAE;YAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgD,GACzD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,uBAAuB,IAAI,qIAAA,CAAA,uBAAoB;QACrD,MAAM,SAAS,MAAM,qBAAqB,YAAY,CAAC,aAAa;QAEpE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,WAAW,IAAI,OAAO,WAAW;QACnC;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QAEvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,WAAW,IAAI,OAAO,WAAW;QACnC,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe;IACpB,MAAM,uBAAuB,IAAI,qIAAA,CAAA,uBAAoB;IACrD,MAAM,YAAY,qBAAqB,YAAY;IAEnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT,MAAM;QACN,WAAW,IAAI,OAAO,WAAW;IACnC;AACF", "debugId": null}}]}