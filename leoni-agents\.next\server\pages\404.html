<!DOCTYPE html><!--WsWNPj7rY9b__38HFwNNB--><html lang="fr"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/ca5b0344af6fbe2c.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-6325d97008443c31.js"/><script src="/_next/static/chunks/4bd1b696-cf72ae8a39fa05aa.js" async=""></script><script src="/_next/static/chunks/964-d6e2a37b7965f281.js" async=""></script><script src="/_next/static/chunks/main-app-a4e9da4cb2627252.js" async=""></script><script src="/_next/static/chunks/598-0915b3b2be220ebc.js" async=""></script><script src="/_next/static/chunks/874-437a265a67d6cfee.js" async=""></script><script src="/_next/static/chunks/app/layout-36d5455f7f2eae6a.js" async=""></script><script src="/_next/static/chunks/110-00434082548dfaee.js" async=""></script><script src="/_next/static/chunks/app/error-ba519935ca0333ee.js" async=""></script><script src="/_next/static/chunks/app/not-found-db8394f455dee4e4.js" async=""></script><script src="/_next/static/chunks/app/loading-ffc478a7c2c2f15a.js" async=""></script><script src="/_next/static/chunks/app/global-error-a450fa68e65b8143.js" async=""></script><meta name="robots" content="noindex"/><title>Leoni Agents - Système d&#x27;Agents Intelligents</title><meta name="description" content="Système d&#x27;agents pour l&#x27;analyse d&#x27;erreurs et la génération SQL"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_5cfdac __variable_9a8899 antialiased bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 min-h-screen"><div hidden=""><!--$--><!--/$--></div><div class="relative min-h-screen"><div class="absolute inset-0 opacity-30"><div class="absolute inset-0 bg-gradient-to-r from-blue-100/50 to-purple-100/50"></div><div class="absolute inset-0" style="background-image:radial-gradient(circle at 1px 1px, rgba(99, 102, 241, 0.1) 1px, transparent 0);background-size:20px 20px"></div></div><nav class="relative"><div class="bg-gradient-to-r from-[#002857] to-[#003d7a] shadow-xl shadow-[#002857]/20 border-b border-[#003d7a]/30"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex justify-between h-20 items-center"><div class="flex items-center space-x-3"><div class="relative"><div class="w-12 h-12 bg-gradient-to-r from-[#ff7514] to-[#ff8c42] rounded-xl flex items-center justify-center shadow-lg shadow-[#ff7514]/30"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sparkles w-7 h-7 text-white" aria-hidden="true"><path d="M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z"></path><path d="M20 2v4"></path><path d="M22 4h-4"></path><circle cx="4" cy="20" r="2"></circle></svg></div><div class="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-[#ff8c42] to-[#ff7514] rounded-full animate-pulse"></div></div><div><h1 class="text-2xl font-bold text-white">Leoni Agents</h1><p class="text-xs text-[#ff8c42] font-medium">Intelligence Artificielle</p></div></div><div class="hidden sm:flex sm:space-x-2"><a class="group relative inline-flex items-center px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 transform hover:scale-105 text-white/80 hover:text-white hover:bg-white/10 hover:shadow-md" href="/"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-house w-5 h-5 mr-2 transition-all duration-300 text-white/70 group-hover:text-[#ff8c42]" aria-hidden="true"><path d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8"></path><path d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path></svg>Accueil</a><a class="group relative inline-flex items-center px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 transform hover:scale-105 text-white/80 hover:text-white hover:bg-white/10 hover:shadow-md" href="/error-analysis"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bug w-5 h-5 mr-2 transition-all duration-300 text-white/70 group-hover:text-[#ff8c42]" aria-hidden="true"><path d="m8 2 1.88 1.88"></path><path d="M14.12 3.88 16 2"></path><path d="M9 7.13v-1a3.003 3.003 0 1 1 6 0v1"></path><path d="M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6"></path><path d="M12 20v-9"></path><path d="M6.53 9C4.6 8.8 3 7.1 3 5"></path><path d="M6 13H2"></path><path d="M3 21c0-2.1 1.7-3.9 3.8-4"></path><path d="M20.97 5c0 2.1-1.6 3.8-3.5 4"></path><path d="M22 13h-4"></path><path d="M17.2 17c2.1.1 3.8 1.9 3.8 4"></path></svg>Analyse d&#x27;Erreurs</a><a class="group relative inline-flex items-center px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 transform hover:scale-105 text-white/80 hover:text-white hover:bg-white/10 hover:shadow-md" href="/sql-generation"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database w-5 h-5 mr-2 transition-all duration-300 text-white/70 group-hover:text-[#ff8c42]" aria-hidden="true"><ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M3 5V19A9 3 0 0 0 21 19V5"></path><path d="M3 12A9 3 0 0 0 21 12"></path></svg>Génération SQL</a></div></div></div></div><div class="sm:hidden px-4 pt-2 pb-3 space-y-1"><a class="block pl-3 pr-4 py-2 border-l-4 text-base font-medium border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700" href="/"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-house w-4 h-4 mr-3" aria-hidden="true"><path d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8"></path><path d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path></svg>Accueil</div></a><a class="block pl-3 pr-4 py-2 border-l-4 text-base font-medium border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700" href="/error-analysis"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bug w-4 h-4 mr-3" aria-hidden="true"><path d="m8 2 1.88 1.88"></path><path d="M14.12 3.88 16 2"></path><path d="M9 7.13v-1a3.003 3.003 0 1 1 6 0v1"></path><path d="M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6"></path><path d="M12 20v-9"></path><path d="M6.53 9C4.6 8.8 3 7.1 3 5"></path><path d="M6 13H2"></path><path d="M3 21c0-2.1 1.7-3.9 3.8-4"></path><path d="M20.97 5c0 2.1-1.6 3.8-3.5 4"></path><path d="M22 13h-4"></path><path d="M17.2 17c2.1.1 3.8 1.9 3.8 4"></path></svg>Analyse d&#x27;Erreurs</div></a><a class="block pl-3 pr-4 py-2 border-l-4 text-base font-medium border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700" href="/sql-generation"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database w-4 h-4 mr-3" aria-hidden="true"><ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M3 5V19A9 3 0 0 0 21 19V5"></path><path d="M3 12A9 3 0 0 0 21 12"></path></svg>Génération SQL</div></a></div></nav><main class="relative z-10"><!--$--><div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100"><div class="max-w-lg w-full mx-4"><div class="bg-white rounded-2xl shadow-xl p-8 text-center"><div class="w-20 h-20 bg-gradient-to-r from-[#002857] to-[#003d7a] rounded-full flex items-center justify-center mx-auto mb-6"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search w-10 h-10 text-white" aria-hidden="true"><path d="m21 21-4.34-4.34"></path><circle cx="11" cy="11" r="8"></circle></svg></div><h1 class="text-6xl font-bold text-[#002857] mb-4">404</h1><h2 class="text-2xl font-bold text-gray-900 mb-4">Page non trouvée</h2><p class="text-gray-600 mb-8">Désolé, la page que vous recherchez n&#x27;existe pas ou a été déplacée.</p><div class="space-y-3"><a href="/"><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-primary/90 h-10 px-4 py-2 w-full bg-gradient-to-r from-[#ff7514] to-[#ff8c42] hover:from-[#e6650f] hover:to-[#ff7514] text-white"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-house w-4 h-4 mr-2" aria-hidden="true"><path d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8"></path><path d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path></svg>Retour à l&#x27;accueil</button></a><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background h-10 px-4 py-2 w-full border-[#002857] text-[#002857] hover:bg-[#002857] hover:text-white"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left w-4 h-4 mr-2" aria-hidden="true"><path d="m12 19-7-7 7-7"></path><path d="M19 12H5"></path></svg>Page précédente</button></div><div class="mt-8 pt-6 border-t border-gray-200"><p class="text-sm text-gray-500">Besoin d&#x27;aide ? Contactez notre équipe support.</p></div></div></div></div><!--$--><!--/$--><!--/$--></main><div class="fixed top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl animate-pulse"></div><div class="fixed bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-indigo-400/20 to-pink-400/20 rounded-full blur-xl animate-pulse delay-1000"></div></div><script src="/_next/static/chunks/webpack-6325d97008443c31.js" id="_R_" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[993,[\"598\",\"static/chunks/598-0915b3b2be220ebc.js\",\"874\",\"static/chunks/874-437a265a67d6cfee.js\",\"177\",\"static/chunks/app/layout-36d5455f7f2eae6a.js\"],\"default\"]\n3:I[7555,[],\"\"]\n4:I[1901,[\"598\",\"static/chunks/598-0915b3b2be220ebc.js\",\"110\",\"static/chunks/110-00434082548dfaee.js\",\"39\",\"static/chunks/app/error-ba519935ca0333ee.js\"],\"default\"]\n5:I[1295,[],\"\"]\n6:I[1544,[\"598\",\"static/chunks/598-0915b3b2be220ebc.js\",\"874\",\"static/chunks/874-437a265a67d6cfee.js\",\"345\",\"static/chunks/app/not-found-db8394f455dee4e4.js\"],\"default\"]\n7:I[894,[],\"ClientPageRoot\"]\na:I[9665,[],\"OutletBoundary\"]\nc:I[4911,[],\"AsyncMetadataOutlet\"]\ne:I[8460,[\"209\",\"static/chunks/app/loading-ffc478a7c2c2f15a.js\"],\"default\"]\nf:I[9665,[],\"ViewportBoundary\"]\n11:I[9665,[],\"MetadataBoundary\"]\n12:\"$Sreact.suspense\"\n14:I[8385,[\"219\",\"static/chunks/app/global-error-a450fa68e65b8143.js\"],\"default\"]\n:HL[\"/_next/static/css/ca5b0344af6fbe2c.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"WsWNPj7rY9b__38HFwNNB\",\"p\":\"\",\"c\":[\"\",\"_not-found\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"/_not-found\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/ca5b0344af6fbe2c.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"fr\",\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_5cfdac __variable_9a8899 antialiased bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 min-h-screen\",\"children\":[\"$\",\"div\",null,{\"className\":\"relative min-h-screen\",\"children\":[[\"$\",\"div\",null,{\"className\":\"absolute inset-0 opacity-30\",\"children\":[[\"$\",\"div\",null,{\"className\":\"absolute inset-0 bg-gradient-to-r from-blue-100/50 to-purple-100/50\"}],[\"$\",\"div\",null,{\"className\":\"absolute inset-0\",\"style\":{\"backgroundImage\":\"radial-gradient(circle at 1px 1px, rgba(99, 102, 241, 0.1) 1px, transparent 0)\",\"backgroundSize\":\"20px 20px\"}}]]}],[\"$\",\"$L2\",null,{}],[\"$\",\"main\",null,{\"className\":\"relative z-10\",\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$4\",\"errorStyles\":[],\"errorScripts\":[],\"template\":[\"$\",\"$L5\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"$L6\",null,{}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"div\",null,{\"className\":\"fixed top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl animate-pulse\"}],[\"$\",\"div\",null,{\"className\":\"fixed bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-indigo-400/20 to-pink-400/20 rounded-full blur-xl animate-pulse delay-1000\"}]]}]}]}]]}],{\"children\":[\"/_not-found\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L5\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L7\",null,{\"Component\":\"$6\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@8\",\"$@9\"]}],null,[\"$\",\"$La\",null,{\"children\":[\"$Lb\",[\"$\",\"$Lc\",null,{\"promise\":\"$@d\"}]]}]]}],{},null,false]},null,false]},[[\"$\",\"$Le\",\"l\",{}],[],[]],false],[\"$\",\"$1\",\"h\",{\"children\":[[\"$\",\"meta\",null,{\"name\":\"robots\",\"content\":\"noindex\"}],[[\"$\",\"$Lf\",null,{\"children\":\"$L10\"}],null],[\"$\",\"$L11\",null,{\"children\":[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$12\",null,{\"fallback\":null,\"children\":\"$L13\"}]}]}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$14\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"8:{}\n9:\"$0:f:0:1:2:children:2:children:1:props:children:0:props:params\"\n"])</script><script>self.__next_f.push([1,"10:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nb:null\n"])</script><script>self.__next_f.push([1,"15:I[8175,[],\"IconMark\"]\nd:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Leoni Agents - Système d'Agents Intelligents\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Système d'agents pour l'analyse d'erreurs et la génération SQL\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}],[\"$\",\"$L15\",\"3\",{}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"13:\"$d:metadata\"\n"])</script></body></html>