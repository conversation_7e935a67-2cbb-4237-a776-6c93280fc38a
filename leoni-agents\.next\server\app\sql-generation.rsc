1:"$Sreact.fragment"
2:I[993,["598","static/chunks/598-0915b3b2be220ebc.js","874","static/chunks/874-437a265a67d6cfee.js","177","static/chunks/app/layout-36d5455f7f2eae6a.js"],"default"]
3:I[7555,[],""]
4:I[1901,["598","static/chunks/598-0915b3b2be220ebc.js","110","static/chunks/110-00434082548dfaee.js","39","static/chunks/app/error-ba519935ca0333ee.js"],"default"]
5:I[1295,[],""]
6:I[1544,["598","static/chunks/598-0915b3b2be220ebc.js","874","static/chunks/874-437a265a67d6cfee.js","345","static/chunks/app/not-found-db8394f455dee4e4.js"],"default"]
7:I[4285,["598","static/chunks/598-0915b3b2be220ebc.js","874","static/chunks/874-437a265a67d6cfee.js","110","static/chunks/110-00434082548dfaee.js","503","static/chunks/app/sql-generation/error-e1b3260c166c636b.js"],"default"]
8:I[894,[],"ClientPageRoot"]
9:I[668,["598","static/chunks/598-0915b3b2be220ebc.js","446","static/chunks/app/sql-generation/page-1c3ab5c3a68e598d.js"],"default"]
c:I[9665,[],"OutletBoundary"]
e:I[4911,[],"AsyncMetadataOutlet"]
10:I[8460,["209","static/chunks/app/loading-ffc478a7c2c2f15a.js"],"default"]
11:I[9665,[],"ViewportBoundary"]
13:I[9665,[],"MetadataBoundary"]
14:"$Sreact.suspense"
16:I[8385,["219","static/chunks/app/global-error-a450fa68e65b8143.js"],"default"]
:HL["/_next/static/css/ca5b0344af6fbe2c.css","style"]
0:{"P":null,"b":"WsWNPj7rY9b__38HFwNNB","p":"","c":["","sql-generation"],"i":false,"f":[[["",{"children":["sql-generation",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/ca5b0344af6fbe2c.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"fr","children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 antialiased bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 min-h-screen","children":["$","div",null,{"className":"relative min-h-screen","children":[["$","div",null,{"className":"absolute inset-0 opacity-30","children":[["$","div",null,{"className":"absolute inset-0 bg-gradient-to-r from-blue-100/50 to-purple-100/50"}],["$","div",null,{"className":"absolute inset-0","style":{"backgroundImage":"radial-gradient(circle at 1px 1px, rgba(99, 102, 241, 0.1) 1px, transparent 0)","backgroundSize":"20px 20px"}}]]}],["$","$L2",null,{}],["$","main",null,{"className":"relative z-10","children":["$","$L3",null,{"parallelRouterKey":"children","error":"$4","errorStyles":[],"errorScripts":[],"template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","$L6",null,{}],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","div",null,{"className":"fixed top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl animate-pulse"}],["$","div",null,{"className":"fixed bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-indigo-400/20 to-pink-400/20 rounded-full blur-xl animate-pulse delay-1000"}]]}]}]}]]}],{"children":["sql-generation",["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$7","errorStyles":[],"errorScripts":[],"template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L8",null,{"Component":"$9","searchParams":{},"params":{},"promises":["$@a","$@b"]}],null,["$","$Lc",null,{"children":["$Ld",["$","$Le",null,{"promise":"$@f"}]]}]]}],{},null,false]},null,false]},[["$","$L10","l",{}],[],[]],false],["$","$1","h",{"children":[null,[["$","$L11",null,{"children":"$L12"}],null],["$","$L13",null,{"children":["$","div",null,{"hidden":true,"children":["$","$14",null,{"fallback":null,"children":"$L15"}]}]}]]}],false]],"m":"$undefined","G":["$16",[]],"s":false,"S":true}
a:{}
b:"$0:f:0:1:2:children:2:children:1:props:children:0:props:params"
12:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
d:null
17:I[8175,[],"IconMark"]
f:{"metadata":[["$","title","0",{"children":"Leoni Agents - Système d'Agents Intelligents"}],["$","meta","1",{"name":"description","content":"Système d'agents pour l'analyse d'erreurs et la génération SQL"}],["$","link","2",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$L17","3",{}]],"error":null,"digest":"$undefined"}
15:"$f:metadata"
