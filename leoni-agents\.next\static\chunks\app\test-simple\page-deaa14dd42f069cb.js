(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[352],{2234:(e,n,i)=>{"use strict";i.r(n),i.d(n,{default:()=>o});var r=i(5155);function o(){return(0,r.jsxs)("div",{style:{padding:"20px"},children:[(0,r.jsx)("h1",{children:"Test Ultra-Simple"}),(0,r.jsx)("p",{children:"Si vous voyez cette page, React fonctionne."}),(0,r.jsx)("button",{onClick:()=>alert("BOUTON FONCTIONNE !"),style:{padding:"10px 20px",backgroundColor:"green",color:"white",border:"none",borderRadius:"5px",cursor:"pointer",fontSize:"16px"},children:"Cliquez-moi !"}),(0,r.jsx)("div",{style:{marginTop:"20px"},children:(0,r.jsx)("p",{children:"Si le bouton ne fonctionne pas, il y a un probl\xe8me JavaScript global."})})]})}},5068:(e,n,i)=>{Promise.resolve().then(i.bind(i,2234))}},e=>{e.O(0,[441,964,358],()=>e(e.s=5068)),_N_E=e.O()}]);