"use strict";exports.id=694,exports.ids=[694],exports.modules={40694:(a,b,c)=>{var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ab,ac,ad,ae,af,ag,ah,ai,aj,ak,al,am,an,ao,ap,aq,ar,as,at,au,av,aw,ax,ay;let az,aA,aB,aC;function aD(a,b,c,d,e){if("m"===d)throw TypeError("Private method is not writable");if("a"===d&&!e)throw TypeError("Private accessor was defined without a setter");if("function"==typeof b?a!==b||!e:!b.has(a))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===d?e.call(a,c):e?e.value=c:b.set(a,c),c}function aE(a,b,c,d){if("a"===c&&!d)throw TypeError("Private accessor was defined without a getter");if("function"==typeof b?a!==b||!d:!b.has(a))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===c?d:"a"===c?d.call(a):d?d.value:b.get(a)}c.d(b,{Ay:()=>c1});let aF=function(){let{crypto:a}=globalThis;if(a?.randomUUID)return aF=a.randomUUID.bind(a),a.randomUUID();let b=new Uint8Array(1),c=a?()=>a.getRandomValues(b)[0]:()=>255*Math.random()&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,a=>(a^c()&15>>a/4).toString(16))};function aG(a){return"object"==typeof a&&null!==a&&("name"in a&&"AbortError"===a.name||"message"in a&&String(a.message).includes("FetchRequestCanceledException"))}let aH=a=>{if(a instanceof Error)return a;if("object"==typeof a&&null!==a){try{if("[object Error]"===Object.prototype.toString.call(a)){let b=Error(a.message,a.cause?{cause:a.cause}:{});return a.stack&&(b.stack=a.stack),a.cause&&!b.cause&&(b.cause=a.cause),a.name&&(b.name=a.name),b}}catch{}try{return Error(JSON.stringify(a))}catch{}}return Error(a)};class aI extends Error{}class aJ extends aI{constructor(a,b,c,d){super(`${aJ.makeMessage(a,b,c)}`),this.status=a,this.headers=d,this.requestID=d?.get("x-request-id"),this.error=b,this.code=b?.code,this.param=b?.param,this.type=b?.type}static makeMessage(a,b,c){let d=b?.message?"string"==typeof b.message?b.message:JSON.stringify(b.message):b?JSON.stringify(b):c;return a&&d?`${a} ${d}`:a?`${a} status code (no body)`:d||"(no status code or body)"}static generate(a,b,c,d){if(!a||!d)return new aL({message:c,cause:aH(b)});let e=b?.error;return 400===a?new aN(a,e,c,d):401===a?new aO(a,e,c,d):403===a?new aP(a,e,c,d):404===a?new aQ(a,e,c,d):409===a?new aR(a,e,c,d):422===a?new aS(a,e,c,d):429===a?new aT(a,e,c,d):a>=500?new aU(a,e,c,d):new aJ(a,e,c,d)}}class aK extends aJ{constructor({message:a}={}){super(void 0,void 0,a||"Request was aborted.",void 0)}}class aL extends aJ{constructor({message:a,cause:b}){super(void 0,void 0,a||"Connection error.",void 0),b&&(this.cause=b)}}class aM extends aL{constructor({message:a}={}){super({message:a??"Request timed out."})}}class aN extends aJ{}class aO extends aJ{}class aP extends aJ{}class aQ extends aJ{}class aR extends aJ{}class aS extends aJ{}class aT extends aJ{}class aU extends aJ{}class aV extends aI{constructor(){super("Could not parse response content as the length limit was reached")}}class aW extends aI{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}class aX extends Error{constructor(a){super(a)}}let aY=/^[a-z][a-z0-9+.-]*:/i,aZ=a=>(aZ=Array.isArray)(a),a$=aZ;function a_(a){return null!=a&&"object"==typeof a&&!Array.isArray(a)}let a0=a=>new Promise(b=>setTimeout(b,a)),a1="5.10.2",a2=a=>"x32"===a?"x32":"x86_64"===a||"x64"===a?"x64":"arm"===a?"arm":"aarch64"===a||"arm64"===a?"arm64":a?`other:${a}`:"unknown",a3=a=>(a=a.toLowerCase()).includes("ios")?"iOS":"android"===a?"Android":"darwin"===a?"MacOS":"win32"===a?"Windows":"freebsd"===a?"FreeBSD":"openbsd"===a?"OpenBSD":"linux"===a?"Linux":a?`Other:${a}`:"Unknown";function a4(...a){let b=globalThis.ReadableStream;if(void 0===b)throw Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new b(...a)}function a5(a){let b=Symbol.asyncIterator in a?a[Symbol.asyncIterator]():a[Symbol.iterator]();return a4({start(){},async pull(a){let{done:c,value:d}=await b.next();c?a.close():a.enqueue(d)},async cancel(){await b.return?.()}})}function a6(a){if(a[Symbol.asyncIterator])return a;let b=a.getReader();return{async next(){try{let a=await b.read();return a?.done&&b.releaseLock(),a}catch(a){throw b.releaseLock(),a}},async return(){let a=b.cancel();return b.releaseLock(),await a,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function a7(a){if(null===a||"object"!=typeof a)return;if(a[Symbol.asyncIterator])return void await a[Symbol.asyncIterator]().return?.();let b=a.getReader(),c=b.cancel();b.releaseLock(),await c}let a8=({headers:a,body:b})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(b)}),a9="RFC3986",ba=a=>String(a),bb={RFC1738:a=>String(a).replace(/%20/g,"+"),RFC3986:ba},bc=(a,b)=>(bc=Object.hasOwn??Function.prototype.call.bind(Object.prototype.hasOwnProperty))(a,b),bd=(()=>{let a=[];for(let b=0;b<256;++b)a.push("%"+((b<16?"0":"")+b.toString(16)).toUpperCase());return a})();function be(a,b){if(aZ(a)){let c=[];for(let d=0;d<a.length;d+=1)c.push(b(a[d]));return c}return b(a)}let bf={brackets:a=>String(a)+"[]",comma:"comma",indices:(a,b)=>String(a)+"["+b+"]",repeat:a=>String(a)},bg=function(a,b){Array.prototype.push.apply(a,aZ(b)?b:[b])},bh={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:(a,b,c,d,e)=>{if(0===a.length)return a;let f=a;if("symbol"==typeof a?f=Symbol.prototype.toString.call(a):"string"!=typeof a&&(f=String(a)),"iso-8859-1"===c)return escape(f).replace(/%u[0-9a-f]{4}/gi,function(a){return"%26%23"+parseInt(a.slice(2),16)+"%3B"});let g="";for(let a=0;a<f.length;a+=1024){let b=f.length>=1024?f.slice(a,a+1024):f,c=[];for(let a=0;a<b.length;++a){let d=b.charCodeAt(a);if(45===d||46===d||95===d||126===d||d>=48&&d<=57||d>=65&&d<=90||d>=97&&d<=122||"RFC1738"===e&&(40===d||41===d)){c[c.length]=b.charAt(a);continue}if(d<128){c[c.length]=bd[d];continue}if(d<2048){c[c.length]=bd[192|d>>6]+bd[128|63&d];continue}if(d<55296||d>=57344){c[c.length]=bd[224|d>>12]+bd[128|d>>6&63]+bd[128|63&d];continue}a+=1,d=65536+((1023&d)<<10|1023&b.charCodeAt(a)),c[c.length]=bd[240|d>>18]+bd[128|d>>12&63]+bd[128|d>>6&63]+bd[128|63&d]}g+=c.join("")}return g},encodeValuesOnly:!1,format:a9,formatter:ba,indices:!1,serializeDate:a=>(aA??(aA=Function.prototype.call.bind(Date.prototype.toISOString)))(a),skipNulls:!1,strictNullHandling:!1},bi={};function bj(a){let b;return(aB??(aB=(b=new globalThis.TextEncoder).encode.bind(b)))(a)}function bk(a){let b;return(aC??(aC=(b=new globalThis.TextDecoder).decode.bind(b)))(a)}class bl{constructor(){d.set(this,void 0),e.set(this,void 0),aD(this,d,new Uint8Array,"f"),aD(this,e,null,"f")}decode(a){let b;if(null==a)return[];let c=a instanceof ArrayBuffer?new Uint8Array(a):"string"==typeof a?bj(a):a;aD(this,d,function(a){let b=0;for(let c of a)b+=c.length;let c=new Uint8Array(b),d=0;for(let b of a)c.set(b,d),d+=b.length;return c}([aE(this,d,"f"),c]),"f");let f=[];for(;null!=(b=function(a,b){for(let c=b??0;c<a.length;c++){if(10===a[c])return{preceding:c,index:c+1,carriage:!1};if(13===a[c])return{preceding:c,index:c+1,carriage:!0}}return null}(aE(this,d,"f"),aE(this,e,"f")));){if(b.carriage&&null==aE(this,e,"f")){aD(this,e,b.index,"f");continue}if(null!=aE(this,e,"f")&&(b.index!==aE(this,e,"f")+1||b.carriage)){f.push(bk(aE(this,d,"f").subarray(0,aE(this,e,"f")-1))),aD(this,d,aE(this,d,"f").subarray(aE(this,e,"f")),"f"),aD(this,e,null,"f");continue}let a=null!==aE(this,e,"f")?b.preceding-1:b.preceding,c=bk(aE(this,d,"f").subarray(0,a));f.push(c),aD(this,d,aE(this,d,"f").subarray(b.index),"f"),aD(this,e,null,"f")}return f}flush(){return aE(this,d,"f").length?this.decode("\n"):[]}}d=new WeakMap,e=new WeakMap,bl.NEWLINE_CHARS=new Set(["\n","\r"]),bl.NEWLINE_REGEXP=/\r\n|[\n\r]/g;let bm={off:0,error:200,warn:300,info:400,debug:500},bn=(a,b,c)=>{if(a){if(Object.prototype.hasOwnProperty.call(bm,a))return a;bs(c).warn(`${b} was set to ${JSON.stringify(a)}, expected one of ${JSON.stringify(Object.keys(bm))}`)}};function bo(){}function bp(a,b,c){return!b||bm[a]>bm[c]?bo:b[a].bind(b)}let bq={error:bo,warn:bo,info:bo,debug:bo},br=new WeakMap;function bs(a){let b=a.logger,c=a.logLevel??"off";if(!b)return bq;let d=br.get(b);if(d&&d[0]===c)return d[1];let e={error:bp("error",b,c),warn:bp("warn",b,c),info:bp("info",b,c),debug:bp("debug",b,c)};return br.set(b,[c,e]),e}let bt=a=>(a.options&&(a.options={...a.options},delete a.options.headers),a.headers&&(a.headers=Object.fromEntries((a.headers instanceof Headers?[...a.headers]:Object.entries(a.headers)).map(([a,b])=>[a,"authorization"===a.toLowerCase()||"cookie"===a.toLowerCase()||"set-cookie"===a.toLowerCase()?"***":b]))),"retryOfRequestLogID"in a&&(a.retryOfRequestLogID&&(a.retryOf=a.retryOfRequestLogID),delete a.retryOfRequestLogID),a);class bu{constructor(a,b,c){this.iterator=a,f.set(this,void 0),this.controller=b,aD(this,f,c,"f")}static fromSSEResponse(a,b,c){let d=!1,e=c?bs(c):console;async function*f(){if(d)throw new aI("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");d=!0;let c=!1;try{for await(let d of bv(a,b))if(!c){if(d.data.startsWith("[DONE]")){c=!0;continue}if(null===d.event||d.event.startsWith("response.")||d.event.startsWith("image_edit.")||d.event.startsWith("image_generation.")||d.event.startsWith("transcript.")){let b;try{b=JSON.parse(d.data)}catch(a){throw e.error("Could not parse message into JSON:",d.data),e.error("From chunk:",d.raw),a}if(b&&b.error)throw new aJ(void 0,b.error,void 0,a.headers);yield b}else{let a;try{a=JSON.parse(d.data)}catch(a){throw console.error("Could not parse message into JSON:",d.data),console.error("From chunk:",d.raw),a}if("error"==d.event)throw new aJ(void 0,a.error,a.message,void 0);yield{event:d.event,data:a}}}c=!0}catch(a){if(aG(a))return;throw a}finally{c||b.abort()}}return new bu(f,b,c)}static fromReadableStream(a,b,c){let d=!1;async function*e(){let b=new bl;for await(let c of a6(a))for(let a of b.decode(c))yield a;for(let a of b.flush())yield a}return new bu(async function*(){if(d)throw new aI("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");d=!0;let a=!1;try{for await(let b of e())!a&&b&&(yield JSON.parse(b));a=!0}catch(a){if(aG(a))return;throw a}finally{a||b.abort()}},b,c)}[(f=new WeakMap,Symbol.asyncIterator)](){return this.iterator()}tee(){let a=[],b=[],c=this.iterator(),d=d=>({next:()=>{if(0===d.length){let d=c.next();a.push(d),b.push(d)}return d.shift()}});return[new bu(()=>d(a),this.controller,aE(this,f,"f")),new bu(()=>d(b),this.controller,aE(this,f,"f"))]}toReadableStream(){let a,b=this;return a4({async start(){a=b[Symbol.asyncIterator]()},async pull(b){try{let{value:c,done:d}=await a.next();if(d)return b.close();let e=bj(JSON.stringify(c)+"\n");b.enqueue(e)}catch(a){b.error(a)}},async cancel(){await a.return?.()}})}}async function*bv(a,b){if(!a.body){if(b.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new aI("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new aI("Attempted to iterate over a response with no body")}let c=new bx,d=new bl;for await(let b of bw(a6(a.body)))for(let a of d.decode(b)){let b=c.decode(a);b&&(yield b)}for(let a of d.flush()){let b=c.decode(a);b&&(yield b)}}async function*bw(a){let b=new Uint8Array;for await(let c of a){let a;if(null==c)continue;let d=c instanceof ArrayBuffer?new Uint8Array(c):"string"==typeof c?bj(c):c,e=new Uint8Array(b.length+d.length);for(e.set(b),e.set(d,b.length),b=e;-1!==(a=function(a){for(let b=0;b<a.length-1;b++){if(10===a[b]&&10===a[b+1]||13===a[b]&&13===a[b+1])return b+2;if(13===a[b]&&10===a[b+1]&&b+3<a.length&&13===a[b+2]&&10===a[b+3])return b+4}return -1}(b));)yield b.slice(0,a),b=b.slice(a)}b.length>0&&(yield b)}class bx{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(a){if(a.endsWith("\r")&&(a=a.substring(0,a.length-1)),!a){if(!this.event&&!this.data.length)return null;let a={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],a}if(this.chunks.push(a),a.startsWith(":"))return null;let[b,c,d]=function(a,b){let c=a.indexOf(":");return -1!==c?[a.substring(0,c),b,a.substring(c+b.length)]:[a,"",""]}(a,":");return d.startsWith(" ")&&(d=d.substring(1)),"event"===b?this.event=d:"data"===b&&this.data.push(d),null}}async function by(a,b){let{response:c,requestLogID:d,retryOfRequestLogID:e,startTime:f}=b,g=await (async()=>{if(b.options.stream)return(bs(a).debug("response",c.status,c.url,c.headers,c.body),b.options.__streamClass)?b.options.__streamClass.fromSSEResponse(c,b.controller,a):bu.fromSSEResponse(c,b.controller,a);if(204===c.status)return null;if(b.options.__binaryResponse)return c;let d=c.headers.get("content-type"),e=d?.split(";")[0]?.trim();return e?.includes("application/json")||e?.endsWith("+json")?bz(await c.json(),c):await c.text()})();return bs(a).debug(`[${d}] response parsed`,bt({retryOfRequestLogID:e,url:c.url,status:c.status,body:g,durationMs:Date.now()-f})),g}function bz(a,b){return!a||"object"!=typeof a||Array.isArray(a)?a:Object.defineProperty(a,"_request_id",{value:b.headers.get("x-request-id"),enumerable:!1})}class bA extends Promise{constructor(a,b,c=by){super(a=>{a(null)}),this.responsePromise=b,this.parseResponse=c,g.set(this,void 0),aD(this,g,a,"f")}_thenUnwrap(a){return new bA(aE(this,g,"f"),this.responsePromise,async(b,c)=>bz(a(await this.parseResponse(b,c),c),c.response))}asResponse(){return this.responsePromise.then(a=>a.response)}async withResponse(){let[a,b]=await Promise.all([this.parse(),this.asResponse()]);return{data:a,response:b,request_id:b.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(a=>this.parseResponse(aE(this,g,"f"),a))),this.parsedPromise}then(a,b){return this.parse().then(a,b)}catch(a){return this.parse().catch(a)}finally(a){return this.parse().finally(a)}}g=new WeakMap;class bB{constructor(a,b,c,d){h.set(this,void 0),aD(this,h,a,"f"),this.options=d,this.response=b,this.body=c}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageRequestOptions()}async getNextPage(){let a=this.nextPageRequestOptions();if(!a)throw new aI("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await aE(this,h,"f").requestAPIList(this.constructor,a)}async *iterPages(){let a=this;for(yield a;a.hasNextPage();)a=await a.getNextPage(),yield a}async *[(h=new WeakMap,Symbol.asyncIterator)](){for await(let a of this.iterPages())for(let b of a.getPaginatedItems())yield b}}class bC extends bA{constructor(a,b,c){super(a,b,async(a,b)=>new c(a,b.response,await by(a,b),b.options))}async *[Symbol.asyncIterator](){for await(let a of(await this))yield a}}class bD extends bB{constructor(a,b,c,d){super(a,b,c,d),this.data=c.data||[],this.object=c.object}getPaginatedItems(){return this.data??[]}nextPageRequestOptions(){return null}}class bE extends bB{constructor(a,b,c,d){super(a,b,c,d),this.data=c.data||[],this.has_more=c.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageRequestOptions(){var a;let b=this.getPaginatedItems(),c=b[b.length-1]?.id;return c?{...this.options,query:{..."object"!=typeof(a=this.options.query)?{}:a??{},after:c}}:null}}let bF=()=>{if("undefined"==typeof File){let{process:a}=globalThis;throw Error("`File` is not defined as a global, which is required for file uploads."+("string"==typeof a?.versions?.node&&20>parseInt(a.versions.node.split("."))?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function bG(a,b,c){return bF(),new File(a,b??"unknown_file",c)}function bH(a){return("object"==typeof a&&null!==a&&("name"in a&&a.name&&String(a.name)||"url"in a&&a.url&&String(a.url)||"filename"in a&&a.filename&&String(a.filename)||"path"in a&&a.path&&String(a.path))||"").split(/[\\/]/).pop()||void 0}let bI=a=>null!=a&&"object"==typeof a&&"function"==typeof a[Symbol.asyncIterator],bJ=async(a,b)=>({...a,body:await bL(a.body,b)}),bK=new WeakMap,bL=async(a,b)=>{if(!await function(a){let b="function"==typeof a?a:a.fetch,c=bK.get(b);if(c)return c;let d=(async()=>{try{let a="Response"in b?b.Response:(await b("data:,")).constructor,c=new FormData;if(c.toString()===await new a(c).text())return!1;return!0}catch{return!0}})();return bK.set(b,d),d}(b))throw TypeError("The provided fetch function does not support file uploads with the current global FormData class.");let c=new FormData;return await Promise.all(Object.entries(a||{}).map(([a,b])=>bO(c,a,b))),c},bM=a=>a instanceof Blob&&"name"in a,bN=a=>{if((a=>"object"==typeof a&&null!==a&&(a instanceof Response||bI(a)||bM(a)))(a))return!0;if(Array.isArray(a))return a.some(bN);if(a&&"object"==typeof a){for(let b in a)if(bN(a[b]))return!0}return!1},bO=async(a,b,c)=>{if(void 0!==c){if(null==c)throw TypeError(`Received null for "${b}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof c||"number"==typeof c||"boolean"==typeof c)a.append(b,String(c));else if(c instanceof Response)a.append(b,bG([await c.blob()],bH(c)));else if(bI(c))a.append(b,bG([await new Response(a5(c)).blob()],bH(c)));else if(bM(c))a.append(b,c,bH(c));else if(Array.isArray(c))await Promise.all(c.map(c=>bO(a,b+"[]",c)));else if("object"==typeof c)await Promise.all(Object.entries(c).map(([c,d])=>bO(a,`${b}[${c}]`,d)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${c} instead`)}},bP=a=>null!=a&&"object"==typeof a&&"number"==typeof a.size&&"string"==typeof a.type&&"function"==typeof a.text&&"function"==typeof a.slice&&"function"==typeof a.arrayBuffer;async function bQ(a,b,c){let d,e;if(bF(),null!=(d=a=await a)&&"object"==typeof d&&"string"==typeof d.name&&"number"==typeof d.lastModified&&bP(d))return a instanceof File?a:bG([await a.arrayBuffer()],a.name);if(null!=(e=a)&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob){let d=await a.blob();return b||(b=new URL(a.url).pathname.split(/[\\/]/).pop()),bG(await bR(d),b,c)}let f=await bR(a);if(b||(b=bH(a)),!c?.type){let a=f.find(a=>"object"==typeof a&&"type"in a&&a.type);"string"==typeof a&&(c={...c,type:a})}return bG(f,b,c)}async function bR(a){let b=[];if("string"==typeof a||ArrayBuffer.isView(a)||a instanceof ArrayBuffer)b.push(a);else if(bP(a))b.push(a instanceof Blob?a:await a.arrayBuffer());else if(bI(a))for await(let c of a)b.push(...await bR(c));else{let b=a?.constructor?.name;throw Error(`Unexpected data type: ${typeof a}${b?`; constructor: ${b}`:""}${function(a){if("object"!=typeof a||null===a)return"";let b=Object.getOwnPropertyNames(a);return`; props: [${b.map(a=>`"${a}"`).join(", ")}]`}(a)}`)}return b}class bS{constructor(a){this._client=a}}function bT(a){return a.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}let bU=Object.freeze(Object.create(null)),bV=((a=bT)=>function(b,...c){let d;if(1===b.length)return b[0];let e=!1,f=[],g=b.reduce((b,d,g)=>{/[?#]/.test(d)&&(e=!0);let h=c[g],i=(e?encodeURIComponent:a)(""+h);return g!==c.length&&(null==h||"object"==typeof h&&h.toString===Object.getPrototypeOf(Object.getPrototypeOf(h.hasOwnProperty??bU)??bU)?.toString)&&(i=h+"",f.push({start:b.length+d.length,length:i.length,error:`Value of type ${Object.prototype.toString.call(h).slice(8,-1)} is not a valid path parameter`})),b+d+(g===c.length?"":i)},""),h=g.split(/[?#]/,1)[0],i=/(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi;for(;null!==(d=i.exec(h));)f.push({start:d.index,length:d[0].length,error:`Value "${d[0]}" can't be safely passed as a path parameter`});if(f.sort((a,b)=>a.start-b.start),f.length>0){let a=0,b=f.reduce((b,c)=>{let d=" ".repeat(c.start-a),e="^".repeat(c.length);return a=c.start+c.length,b+d+e},"");throw new aI(`Path parameters result in path with invalid segments:
${f.map(a=>a.error).join("\n")}
${g}
${b}`)}return g})(bT);class bW extends bS{list(a,b={},c){return this._client.getAPIList(bV`/chat/completions/${a}/messages`,bE,{query:b,...c})}}let bX=a=>a?.role==="assistant",bY=a=>a?.role==="tool";class bZ{constructor(){i.add(this),this.controller=new AbortController,j.set(this,void 0),k.set(this,()=>{}),l.set(this,()=>{}),m.set(this,void 0),n.set(this,()=>{}),o.set(this,()=>{}),p.set(this,{}),q.set(this,!1),r.set(this,!1),s.set(this,!1),t.set(this,!1),aD(this,j,new Promise((a,b)=>{aD(this,k,a,"f"),aD(this,l,b,"f")}),"f"),aD(this,m,new Promise((a,b)=>{aD(this,n,a,"f"),aD(this,o,b,"f")}),"f"),aE(this,j,"f").catch(()=>{}),aE(this,m,"f").catch(()=>{})}_run(a){setTimeout(()=>{a().then(()=>{this._emitFinal(),this._emit("end")},aE(this,i,"m",u).bind(this))},0)}_connected(){this.ended||(aE(this,k,"f").call(this),this._emit("connect"))}get ended(){return aE(this,q,"f")}get errored(){return aE(this,r,"f")}get aborted(){return aE(this,s,"f")}abort(){this.controller.abort()}on(a,b){return(aE(this,p,"f")[a]||(aE(this,p,"f")[a]=[])).push({listener:b}),this}off(a,b){let c=aE(this,p,"f")[a];if(!c)return this;let d=c.findIndex(a=>a.listener===b);return d>=0&&c.splice(d,1),this}once(a,b){return(aE(this,p,"f")[a]||(aE(this,p,"f")[a]=[])).push({listener:b,once:!0}),this}emitted(a){return new Promise((b,c)=>{aD(this,t,!0,"f"),"error"!==a&&this.once("error",c),this.once(a,b)})}async done(){aD(this,t,!0,"f"),await aE(this,m,"f")}_emit(a,...b){if(aE(this,q,"f"))return;"end"===a&&(aD(this,q,!0,"f"),aE(this,n,"f").call(this));let c=aE(this,p,"f")[a];if(c&&(aE(this,p,"f")[a]=c.filter(a=>!a.once),c.forEach(({listener:a})=>a(...b))),"abort"===a){let a=b[0];aE(this,t,"f")||c?.length||Promise.reject(a),aE(this,l,"f").call(this,a),aE(this,o,"f").call(this,a),this._emit("end");return}if("error"===a){let a=b[0];aE(this,t,"f")||c?.length||Promise.reject(a),aE(this,l,"f").call(this,a),aE(this,o,"f").call(this,a),this._emit("end")}}_emitFinal(){}}function b$(a){return a?.$brand==="auto-parseable-response-format"}function b_(a){return a?.$brand==="auto-parseable-tool"}function b0(a,b){let c=a.choices.map(a=>{var c,d;if("length"===a.finish_reason)throw new aV;if("content_filter"===a.finish_reason)throw new aW;return{...a,message:{...a.message,...a.message.tool_calls?{tool_calls:a.message.tool_calls?.map(a=>(function(a,b){let c=a.tools?.find(a=>a.function?.name===b.function.name);return{...b,function:{...b.function,parsed_arguments:b_(c)?c.$parseRaw(b.function.arguments):c?.function.strict?JSON.parse(b.function.arguments):null}}})(b,a))??void 0}:void 0,parsed:a.message.content&&!a.message.refusal?(c=b,d=a.message.content,c.response_format?.type!=="json_schema"?null:c.response_format?.type==="json_schema"?"$parseRaw"in c.response_format?c.response_format.$parseRaw(d):JSON.parse(d):null):null}}});return{...a,choices:c}}function b1(a){return!!b$(a.response_format)||(a.tools?.some(a=>b_(a)||"function"===a.type&&!0===a.function.strict)??!1)}j=new WeakMap,k=new WeakMap,l=new WeakMap,m=new WeakMap,n=new WeakMap,o=new WeakMap,p=new WeakMap,q=new WeakMap,r=new WeakMap,s=new WeakMap,t=new WeakMap,i=new WeakSet,u=function(a){if(aD(this,r,!0,"f"),a instanceof Error&&"AbortError"===a.name&&(a=new aK),a instanceof aK)return aD(this,s,!0,"f"),this._emit("abort",a);if(a instanceof aI)return this._emit("error",a);if(a instanceof Error){let b=new aI(a.message);return b.cause=a,this._emit("error",b)}return this._emit("error",new aI(String(a)))};class b2 extends bZ{constructor(){super(...arguments),v.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(a){this._chatCompletions.push(a),this._emit("chatCompletion",a);let b=a.choices[0]?.message;return b&&this._addMessage(b),a}_addMessage(a,b=!0){if("content"in a||(a.content=null),this.messages.push(a),b){if(this._emit("message",a),bY(a)&&a.content)this._emit("functionToolCallResult",a.content);else if(bX(a)&&a.tool_calls)for(let b of a.tool_calls)"function"===b.type&&this._emit("functionToolCall",b.function)}}async finalChatCompletion(){await this.done();let a=this._chatCompletions[this._chatCompletions.length-1];if(!a)throw new aI("stream ended without producing a ChatCompletion");return a}async finalContent(){return await this.done(),aE(this,v,"m",w).call(this)}async finalMessage(){return await this.done(),aE(this,v,"m",x).call(this)}async finalFunctionToolCall(){return await this.done(),aE(this,v,"m",y).call(this)}async finalFunctionToolCallResult(){return await this.done(),aE(this,v,"m",z).call(this)}async totalUsage(){return await this.done(),aE(this,v,"m",A).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){let a=this._chatCompletions[this._chatCompletions.length-1];a&&this._emit("finalChatCompletion",a);let b=aE(this,v,"m",x).call(this);b&&this._emit("finalMessage",b);let c=aE(this,v,"m",w).call(this);c&&this._emit("finalContent",c);let d=aE(this,v,"m",y).call(this);d&&this._emit("finalFunctionToolCall",d);let e=aE(this,v,"m",z).call(this);null!=e&&this._emit("finalFunctionToolCallResult",e),this._chatCompletions.some(a=>a.usage)&&this._emit("totalUsage",aE(this,v,"m",A).call(this))}async _createChatCompletion(a,b,c){let d=c?.signal;d&&(d.aborted&&this.controller.abort(),d.addEventListener("abort",()=>this.controller.abort())),aE(this,v,"m",B).call(this,b);let e=await a.chat.completions.create({...b,stream:!1},{...c,signal:this.controller.signal});return this._connected(),this._addChatCompletion(b0(e,b))}async _runChatCompletion(a,b,c){for(let a of b.messages)this._addMessage(a,!1);return await this._createChatCompletion(a,b,c)}async _runTools(a,b,c){let d="tool",{tool_choice:e="auto",stream:f,...g}=b,h="string"!=typeof e&&e?.function?.name,{maxChatCompletions:i=10}=c||{},j=b.tools.map(a=>{if(b_(a)){if(!a.$callback)throw new aI("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:a.$callback,name:a.function.name,description:a.function.description||"",parameters:a.function.parameters,parse:a.$parseRaw,strict:!0}}}return a}),k={};for(let a of j)"function"===a.type&&(k[a.function.name||a.function.function.name]=a.function);let l="tools"in b?j.map(a=>"function"===a.type?{type:"function",function:{name:a.function.name||a.function.function.name,parameters:a.function.parameters,description:a.function.description,strict:a.function.strict}}:a):void 0;for(let a of b.messages)this._addMessage(a,!1);for(let b=0;b<i;++b){let b=await this._createChatCompletion(a,{...g,tool_choice:e,tools:l,messages:[...this.messages]},c),f=b.choices[0]?.message;if(!f)throw new aI("missing message in ChatCompletion response");if(!f.tool_calls?.length)break;for(let a of f.tool_calls){let b;if("function"!==a.type)continue;let c=a.id,{name:e,arguments:f}=a.function,g=k[e];if(g){if(h&&h!==e){let a=`Invalid tool_call: ${JSON.stringify(e)}. ${JSON.stringify(h)} requested. Please try again`;this._addMessage({role:d,tool_call_id:c,content:a});continue}}else{let a=`Invalid tool_call: ${JSON.stringify(e)}. Available options are: ${Object.keys(k).map(a=>JSON.stringify(a)).join(", ")}. Please try again`;this._addMessage({role:d,tool_call_id:c,content:a});continue}try{b="function"==typeof g.parse?await g.parse(f):f}catch(b){let a=b instanceof Error?b.message:String(b);this._addMessage({role:d,tool_call_id:c,content:a});continue}let i=await g.function(b,this),j=aE(this,v,"m",C).call(this,i);if(this._addMessage({role:d,tool_call_id:c,content:j}),h)return}}}}v=new WeakSet,w=function(){return aE(this,v,"m",x).call(this).content??null},x=function(){let a=this.messages.length;for(;a-- >0;){let b=this.messages[a];if(bX(b))return{...b,content:b.content??null,refusal:b.refusal??null}}throw new aI("stream ended without producing a ChatCompletionMessage with role=assistant")},y=function(){for(let a=this.messages.length-1;a>=0;a--){let b=this.messages[a];if(bX(b)&&b?.tool_calls?.length)return b.tool_calls.at(-1)?.function}},z=function(){for(let a=this.messages.length-1;a>=0;a--){let b=this.messages[a];if(bY(b)&&null!=b.content&&"string"==typeof b.content&&this.messages.some(a=>"assistant"===a.role&&a.tool_calls?.some(a=>"function"===a.type&&a.id===b.tool_call_id)))return b.content}},A=function(){let a={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(let{usage:b}of this._chatCompletions)b&&(a.completion_tokens+=b.completion_tokens,a.prompt_tokens+=b.prompt_tokens,a.total_tokens+=b.total_tokens);return a},B=function(a){if(null!=a.n&&a.n>1)throw new aI("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},C=function(a){return"string"==typeof a?a:void 0===a?"undefined":JSON.stringify(a)};class b3 extends b2{static runTools(a,b,c){let d=new b3,e={...c,headers:{...c?.headers,"X-Stainless-Helper-Method":"runTools"}};return d._run(()=>d._runTools(a,b,e)),d}_addMessage(a,b=!0){super._addMessage(a,b),bX(a)&&a.content&&this._emit("content",a.content)}}let b4={STR:1,NUM:2,ARR:4,OBJ:8,NULL:16,BOOL:32,NAN:64,INFINITY:128,MINUS_INFINITY:256,INF:384,ALL:511};class b5 extends Error{}class b6 extends Error{}let b7=a=>(function(a,b=b4.ALL){if("string"!=typeof a)throw TypeError(`expecting str, got ${typeof a}`);if(!a.trim())throw Error(`${a} is empty`);return((a,b)=>{let c=a.length,d=0,e=a=>{throw new b5(`${a} at position ${d}`)},f=a=>{throw new b6(`${a} at position ${d}`)},g=()=>(l(),d>=c&&e("Unexpected end of input"),'"'===a[d])?h():"{"===a[d]?i():"["===a[d]?j():"null"===a.substring(d,d+4)||b4.NULL&b&&c-d<4&&"null".startsWith(a.substring(d))?(d+=4,null):"true"===a.substring(d,d+4)||b4.BOOL&b&&c-d<4&&"true".startsWith(a.substring(d))?(d+=4,!0):"false"===a.substring(d,d+5)||b4.BOOL&b&&c-d<5&&"false".startsWith(a.substring(d))?(d+=5,!1):"Infinity"===a.substring(d,d+8)||b4.INFINITY&b&&c-d<8&&"Infinity".startsWith(a.substring(d))?(d+=8,1/0):"-Infinity"===a.substring(d,d+9)||b4.MINUS_INFINITY&b&&1<c-d&&c-d<9&&"-Infinity".startsWith(a.substring(d))?(d+=9,-1/0):"NaN"===a.substring(d,d+3)||b4.NAN&b&&c-d<3&&"NaN".startsWith(a.substring(d))?(d+=3,NaN):k(),h=()=>{let g=d,h=!1;for(d++;d<c&&('"'!==a[d]||h&&"\\"===a[d-1]);)h="\\"===a[d]&&!h,d++;if('"'==a.charAt(d))try{return JSON.parse(a.substring(g,++d-Number(h)))}catch(a){f(String(a))}else if(b4.STR&b)try{return JSON.parse(a.substring(g,d-Number(h))+'"')}catch(b){return JSON.parse(a.substring(g,a.lastIndexOf("\\"))+'"')}e("Unterminated string literal")},i=()=>{d++,l();let f={};try{for(;"}"!==a[d];){if(l(),d>=c&&b4.OBJ&b)return f;let e=h();l(),d++;try{let a=g();Object.defineProperty(f,e,{value:a,writable:!0,enumerable:!0,configurable:!0})}catch(a){if(b4.OBJ&b)return f;throw a}l(),","===a[d]&&d++}}catch(a){if(b4.OBJ&b)return f;e("Expected '}' at end of object")}return d++,f},j=()=>{d++;let c=[];try{for(;"]"!==a[d];)c.push(g()),l(),","===a[d]&&d++}catch(a){if(b4.ARR&b)return c;e("Expected ']' at end of array")}return d++,c},k=()=>{if(0===d){"-"===a&&b4.NUM&b&&e("Not sure what '-' is");try{return JSON.parse(a)}catch(c){if(b4.NUM&b)try{if("."===a[a.length-1])return JSON.parse(a.substring(0,a.lastIndexOf(".")));return JSON.parse(a.substring(0,a.lastIndexOf("e")))}catch(a){}f(String(c))}}let g=d;for("-"===a[d]&&d++;a[d]&&!",]}".includes(a[d]);)d++;d!=c||b4.NUM&b||e("Unterminated number literal");try{return JSON.parse(a.substring(g,d))}catch(c){"-"===a.substring(g,d)&&b4.NUM&b&&e("Not sure what '-' is");try{return JSON.parse(a.substring(g,a.lastIndexOf("e")))}catch(a){f(String(a))}}},l=()=>{for(;d<c&&" \n\r	".includes(a[d]);)d++};return g()})(a.trim(),b)})(a,b4.ALL^b4.NUM);class b8 extends b2{constructor(a){super(),D.add(this),E.set(this,void 0),F.set(this,void 0),G.set(this,void 0),aD(this,E,a,"f"),aD(this,F,[],"f")}get currentChatCompletionSnapshot(){return aE(this,G,"f")}static fromReadableStream(a){let b=new b8(null);return b._run(()=>b._fromReadableStream(a)),b}static createChatCompletion(a,b,c){let d=new b8(b);return d._run(()=>d._runChatCompletion(a,{...b,stream:!0},{...c,headers:{...c?.headers,"X-Stainless-Helper-Method":"stream"}})),d}async _createChatCompletion(a,b,c){super._createChatCompletion;let d=c?.signal;d&&(d.aborted&&this.controller.abort(),d.addEventListener("abort",()=>this.controller.abort())),aE(this,D,"m",H).call(this);let e=await a.chat.completions.create({...b,stream:!0},{...c,signal:this.controller.signal});for await(let a of(this._connected(),e))aE(this,D,"m",J).call(this,a);if(e.controller.signal?.aborted)throw new aK;return this._addChatCompletion(aE(this,D,"m",M).call(this))}async _fromReadableStream(a,b){let c,d=b?.signal;d&&(d.aborted&&this.controller.abort(),d.addEventListener("abort",()=>this.controller.abort())),aE(this,D,"m",H).call(this),this._connected();let e=bu.fromReadableStream(a,this.controller);for await(let a of e)c&&c!==a.id&&this._addChatCompletion(aE(this,D,"m",M).call(this)),aE(this,D,"m",J).call(this,a),c=a.id;if(e.controller.signal?.aborted)throw new aK;return this._addChatCompletion(aE(this,D,"m",M).call(this))}[(E=new WeakMap,F=new WeakMap,G=new WeakMap,D=new WeakSet,H=function(){this.ended||aD(this,G,void 0,"f")},I=function(a){let b=aE(this,F,"f")[a.index];return b||(b={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},aE(this,F,"f")[a.index]=b),b},J=function(a){if(this.ended)return;let b=aE(this,D,"m",O).call(this,a);for(let c of(this._emit("chunk",a,b),a.choices)){let a=b.choices[c.index];null!=c.delta.content&&a.message?.role==="assistant"&&a.message?.content&&(this._emit("content",c.delta.content,a.message.content),this._emit("content.delta",{delta:c.delta.content,snapshot:a.message.content,parsed:a.message.parsed})),null!=c.delta.refusal&&a.message?.role==="assistant"&&a.message?.refusal&&this._emit("refusal.delta",{delta:c.delta.refusal,snapshot:a.message.refusal}),c.logprobs?.content!=null&&a.message?.role==="assistant"&&this._emit("logprobs.content.delta",{content:c.logprobs?.content,snapshot:a.logprobs?.content??[]}),c.logprobs?.refusal!=null&&a.message?.role==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:c.logprobs?.refusal,snapshot:a.logprobs?.refusal??[]});let d=aE(this,D,"m",I).call(this,a);for(let b of(a.finish_reason&&(aE(this,D,"m",L).call(this,a),null!=d.current_tool_call_index&&aE(this,D,"m",K).call(this,a,d.current_tool_call_index)),c.delta.tool_calls??[]))d.current_tool_call_index!==b.index&&(aE(this,D,"m",L).call(this,a),null!=d.current_tool_call_index&&aE(this,D,"m",K).call(this,a,d.current_tool_call_index)),d.current_tool_call_index=b.index;for(let b of c.delta.tool_calls??[]){let c=a.message.tool_calls?.[b.index];c?.type&&(c?.type==="function"?this._emit("tool_calls.function.arguments.delta",{name:c.function?.name,index:b.index,arguments:c.function.arguments,parsed_arguments:c.function.parsed_arguments,arguments_delta:b.function?.arguments??""}):c?.type)}}},K=function(a,b){if(aE(this,D,"m",I).call(this,a).done_tool_calls.has(b))return;let c=a.message.tool_calls?.[b];if(!c)throw Error("no tool call snapshot");if(!c.type)throw Error("tool call snapshot missing `type`");if("function"===c.type){let a=aE(this,E,"f")?.tools?.find(a=>"function"===a.type&&a.function.name===c.function.name);this._emit("tool_calls.function.arguments.done",{name:c.function.name,index:b,arguments:c.function.arguments,parsed_arguments:b_(a)?a.$parseRaw(c.function.arguments):a?.function.strict?JSON.parse(c.function.arguments):null})}else c.type},L=function(a){let b=aE(this,D,"m",I).call(this,a);if(a.message.content&&!b.content_done){b.content_done=!0;let c=aE(this,D,"m",N).call(this);this._emit("content.done",{content:a.message.content,parsed:c?c.$parseRaw(a.message.content):null})}a.message.refusal&&!b.refusal_done&&(b.refusal_done=!0,this._emit("refusal.done",{refusal:a.message.refusal})),a.logprobs?.content&&!b.logprobs_content_done&&(b.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:a.logprobs.content})),a.logprobs?.refusal&&!b.logprobs_refusal_done&&(b.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:a.logprobs.refusal}))},M=function(){if(this.ended)throw new aI("stream has ended, this shouldn't happen");let a=aE(this,G,"f");if(!a)throw new aI("request ended without sending any chunks");return aD(this,G,void 0,"f"),aD(this,F,[],"f"),function(a,b){var c;let{id:d,choices:e,created:f,model:g,system_fingerprint:h,...i}=a;return c={...i,id:d,choices:e.map(({message:b,finish_reason:c,index:d,logprobs:e,...f})=>{if(!c)throw new aI(`missing finish_reason for choice ${d}`);let{content:g=null,function_call:h,tool_calls:i,...j}=b,k=b.role;if(!k)throw new aI(`missing role for choice ${d}`);if(h){let{arguments:a,name:i}=h;if(null==a)throw new aI(`missing function_call.arguments for choice ${d}`);if(!i)throw new aI(`missing function_call.name for choice ${d}`);return{...f,message:{content:g,function_call:{arguments:a,name:i},role:k,refusal:b.refusal??null},finish_reason:c,index:d,logprobs:e}}return i?{...f,index:d,finish_reason:c,logprobs:e,message:{...j,role:k,content:g,refusal:b.refusal??null,tool_calls:i.map((b,c)=>{let{function:e,type:f,id:g,...h}=b,{arguments:i,name:j,...k}=e||{};if(null==g)throw new aI(`missing choices[${d}].tool_calls[${c}].id
${b9(a)}`);if(null==f)throw new aI(`missing choices[${d}].tool_calls[${c}].type
${b9(a)}`);if(null==j)throw new aI(`missing choices[${d}].tool_calls[${c}].function.name
${b9(a)}`);if(null==i)throw new aI(`missing choices[${d}].tool_calls[${c}].function.arguments
${b9(a)}`);return{...h,id:g,type:f,function:{...k,name:j,arguments:i}}})}}:{...f,message:{...j,content:g,role:k,refusal:b.refusal??null},finish_reason:c,index:d,logprobs:e}}),created:f,model:g,object:"chat.completion",...h?{system_fingerprint:h}:{}},b&&b1(b)?b0(c,b):{...c,choices:c.choices.map(a=>({...a,message:{...a.message,parsed:null,...a.message.tool_calls?{tool_calls:a.message.tool_calls}:void 0}}))}}(a,aE(this,E,"f"))},N=function(){let a=aE(this,E,"f")?.response_format;return b$(a)?a:null},O=function(a){var b,c,d,e;let f=aE(this,G,"f"),{choices:g,...h}=a;for(let{delta:g,finish_reason:i,index:j,logprobs:k=null,...l}of(f?Object.assign(f,h):f=aD(this,G,{...h,choices:[]},"f"),a.choices)){let a=f.choices[j];if(a||(a=f.choices[j]={finish_reason:i,index:j,message:{},logprobs:k,...l}),k)if(a.logprobs){let{content:d,refusal:e,...f}=k;Object.assign(a.logprobs,f),d&&((b=a.logprobs).content??(b.content=[]),a.logprobs.content.push(...d)),e&&((c=a.logprobs).refusal??(c.refusal=[]),a.logprobs.refusal.push(...e))}else a.logprobs=Object.assign({},k);if(i&&(a.finish_reason=i,aE(this,E,"f")&&b1(aE(this,E,"f")))){if("length"===i)throw new aV;if("content_filter"===i)throw new aW}if(Object.assign(a,l),!g)continue;let{content:h,refusal:m,function_call:n,role:o,tool_calls:p,...q}=g;if(Object.assign(a.message,q),m&&(a.message.refusal=(a.message.refusal||"")+m),o&&(a.message.role=o),n&&(a.message.function_call?(n.name&&(a.message.function_call.name=n.name),n.arguments&&((d=a.message.function_call).arguments??(d.arguments=""),a.message.function_call.arguments+=n.arguments)):a.message.function_call=n),h&&(a.message.content=(a.message.content||"")+h,!a.message.refusal&&aE(this,D,"m",N).call(this)&&(a.message.parsed=b7(a.message.content))),p)for(let{index:b,id:c,type:d,function:f,...g}of(a.message.tool_calls||(a.message.tool_calls=[]),p)){let h=(e=a.message.tool_calls)[b]??(e[b]={});Object.assign(h,g),c&&(h.id=c),d&&(h.type=d),f&&(h.function??(h.function={name:f.name??"",arguments:""})),f?.name&&(h.function.name=f.name),f?.arguments&&(h.function.arguments+=f.arguments,function(a,b){if(!a)return!1;let c=a.tools?.find(a=>a.function?.name===b.function.name);return b_(c)||c?.function.strict||!1}(aE(this,E,"f"),h)&&(h.function.parsed_arguments=b7(h.function.arguments)))}}return f},Symbol.asyncIterator)](){let a=[],b=[],c=!1;return this.on("chunk",c=>{let d=b.shift();d?d.resolve(c):a.push(c)}),this.on("end",()=>{for(let a of(c=!0,b))a.resolve(void 0);b.length=0}),this.on("abort",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),this.on("error",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),{next:async()=>a.length?{value:a.shift(),done:!1}:c?{value:void 0,done:!0}:new Promise((a,c)=>b.push({resolve:a,reject:c})).then(a=>a?{value:a,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new bu(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function b9(a){return JSON.stringify(a)}class ca extends b8{static fromReadableStream(a){let b=new ca(null);return b._run(()=>b._fromReadableStream(a)),b}static runTools(a,b,c){let d=new ca(b),e={...c,headers:{...c?.headers,"X-Stainless-Helper-Method":"runTools"}};return d._run(()=>d._runTools(a,b,e)),d}}class cb extends bS{constructor(){super(...arguments),this.messages=new bW(this._client)}create(a,b){return this._client.post("/chat/completions",{body:a,...b,stream:a.stream??!1})}retrieve(a,b){return this._client.get(bV`/chat/completions/${a}`,b)}update(a,b,c){return this._client.post(bV`/chat/completions/${a}`,{body:b,...c})}list(a={},b){return this._client.getAPIList("/chat/completions",bE,{query:a,...b})}delete(a,b){return this._client.delete(bV`/chat/completions/${a}`,b)}parse(a,b){for(let b of a.tools??[]){if("function"!==b.type)throw new aI(`Currently only \`function\` tool types support auto-parsing; Received \`${b.type}\``);if(!0!==b.function.strict)throw new aI(`The \`${b.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}return this._client.chat.completions.create(a,{...b,headers:{...b?.headers,"X-Stainless-Helper-Method":"chat.completions.parse"}})._thenUnwrap(b=>b0(b,a))}runTools(a,b){return a.stream?ca.runTools(this._client,a,b):b3.runTools(this._client,a,b)}stream(a,b){return b8.createChatCompletion(this._client,a,b)}}cb.Messages=bW;class cc extends bS{constructor(){super(...arguments),this.completions=new cb(this._client)}}cc.Completions=cb;let cd=Symbol("brand.privateNullableHeaders"),ce=a=>{let b=new Headers,c=new Set;for(let d of a){let a=new Set;for(let[e,f]of function*(a){let b;if(!a)return;if(cd in a){let{values:b,nulls:c}=a;for(let a of(yield*b.entries(),c))yield[a,null];return}let c=!1;for(let d of(a instanceof Headers?b=a.entries():a$(a)?b=a:(c=!0,b=Object.entries(a??{})),b)){let a=d[0];if("string"!=typeof a)throw TypeError("expected header name to be a string");let b=a$(d[1])?d[1]:[d[1]],e=!1;for(let d of b)void 0!==d&&(c&&!e&&(e=!0,yield[a,null]),yield[a,d])}}(d)){let d=e.toLowerCase();a.has(d)||(b.delete(e),a.add(d)),null===f?(b.delete(e),c.add(d)):(b.append(e,f),c.delete(d))}}return{[cd]:!0,values:b,nulls:c}};class cf extends bS{create(a,b){return this._client.post("/audio/speech",{body:a,...b,headers:ce([{Accept:"application/octet-stream"},b?.headers]),__binaryResponse:!0})}}class cg extends bS{create(a,b){return this._client.post("/audio/transcriptions",bJ({body:a,...b,stream:a.stream??!1,__metadata:{model:a.model}},this._client))}}class ch extends bS{create(a,b){return this._client.post("/audio/translations",bJ({body:a,...b,__metadata:{model:a.model}},this._client))}}class ci extends bS{constructor(){super(...arguments),this.transcriptions=new cg(this._client),this.translations=new ch(this._client),this.speech=new cf(this._client)}}ci.Transcriptions=cg,ci.Translations=ch,ci.Speech=cf;class cj extends bS{create(a,b){return this._client.post("/batches",{body:a,...b})}retrieve(a,b){return this._client.get(bV`/batches/${a}`,b)}list(a={},b){return this._client.getAPIList("/batches",bE,{query:a,...b})}cancel(a,b){return this._client.post(bV`/batches/${a}/cancel`,b)}}class ck extends bS{create(a,b){return this._client.post("/assistants",{body:a,...b,headers:ce([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}retrieve(a,b){return this._client.get(bV`/assistants/${a}`,{...b,headers:ce([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}update(a,b,c){return this._client.post(bV`/assistants/${a}`,{body:b,...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a={},b){return this._client.getAPIList("/assistants",bE,{query:a,...b,headers:ce([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}delete(a,b){return this._client.delete(bV`/assistants/${a}`,{...b,headers:ce([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}}class cl extends bS{create(a,b){return this._client.post("/realtime/sessions",{body:a,...b,headers:ce([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}}class cm extends bS{create(a,b){return this._client.post("/realtime/transcription_sessions",{body:a,...b,headers:ce([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}}class cn extends bS{constructor(){super(...arguments),this.sessions=new cl(this._client),this.transcriptionSessions=new cm(this._client)}}cn.Sessions=cl,cn.TranscriptionSessions=cm;class co extends bS{create(a,b,c){return this._client.post(bV`/threads/${a}/messages`,{body:b,...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}retrieve(a,b,c){let{thread_id:d}=b;return this._client.get(bV`/threads/${d}/messages/${a}`,{...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}update(a,b,c){let{thread_id:d,...e}=b;return this._client.post(bV`/threads/${d}/messages/${a}`,{body:e,...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a,b={},c){return this._client.getAPIList(bV`/threads/${a}/messages`,bE,{query:b,...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}delete(a,b,c){let{thread_id:d}=b;return this._client.delete(bV`/threads/${d}/messages/${a}`,{...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}}class cp extends bS{retrieve(a,b,c){let{thread_id:d,run_id:e,...f}=b;return this._client.get(bV`/threads/${d}/runs/${e}/steps/${a}`,{query:f,...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a,b,c){let{thread_id:d,...e}=b;return this._client.getAPIList(bV`/threads/${d}/runs/${a}/steps`,bE,{query:e,...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}}let cq=a=>void 0!==globalThis.process?globalThis.process.env?.[a]?.trim()??void 0:void 0!==globalThis.Deno?globalThis.Deno.env?.get?.(a)?.trim():void 0;class cr extends bZ{constructor(){super(...arguments),P.add(this),R.set(this,[]),S.set(this,{}),T.set(this,{}),U.set(this,void 0),V.set(this,void 0),W.set(this,void 0),X.set(this,void 0),Y.set(this,void 0),Z.set(this,void 0),$.set(this,void 0),_.set(this,void 0),aa.set(this,void 0)}[(R=new WeakMap,S=new WeakMap,T=new WeakMap,U=new WeakMap,V=new WeakMap,W=new WeakMap,X=new WeakMap,Y=new WeakMap,Z=new WeakMap,$=new WeakMap,_=new WeakMap,aa=new WeakMap,P=new WeakSet,Symbol.asyncIterator)](){let a=[],b=[],c=!1;return this.on("event",c=>{let d=b.shift();d?d.resolve(c):a.push(c)}),this.on("end",()=>{for(let a of(c=!0,b))a.resolve(void 0);b.length=0}),this.on("abort",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),this.on("error",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),{next:async()=>a.length?{value:a.shift(),done:!1}:c?{value:void 0,done:!0}:new Promise((a,c)=>b.push({resolve:a,reject:c})).then(a=>a?{value:a,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(a){let b=new Q;return b._run(()=>b._fromReadableStream(a)),b}async _fromReadableStream(a,b){let c=b?.signal;c&&(c.aborted&&this.controller.abort(),c.addEventListener("abort",()=>this.controller.abort())),this._connected();let d=bu.fromReadableStream(a,this.controller);for await(let a of d)aE(this,P,"m",ab).call(this,a);if(d.controller.signal?.aborted)throw new aK;return this._addRun(aE(this,P,"m",ac).call(this))}toReadableStream(){return new bu(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(a,b,c,d){let e=new Q;return e._run(()=>e._runToolAssistantStream(a,b,c,{...d,headers:{...d?.headers,"X-Stainless-Helper-Method":"stream"}})),e}async _createToolAssistantStream(a,b,c,d){let e=d?.signal;e&&(e.aborted&&this.controller.abort(),e.addEventListener("abort",()=>this.controller.abort()));let f={...c,stream:!0},g=await a.submitToolOutputs(b,f,{...d,signal:this.controller.signal});for await(let a of(this._connected(),g))aE(this,P,"m",ab).call(this,a);if(g.controller.signal?.aborted)throw new aK;return this._addRun(aE(this,P,"m",ac).call(this))}static createThreadAssistantStream(a,b,c){let d=new Q;return d._run(()=>d._threadAssistantStream(a,b,{...c,headers:{...c?.headers,"X-Stainless-Helper-Method":"stream"}})),d}static createAssistantStream(a,b,c,d){let e=new Q;return e._run(()=>e._runAssistantStream(a,b,c,{...d,headers:{...d?.headers,"X-Stainless-Helper-Method":"stream"}})),e}currentEvent(){return aE(this,$,"f")}currentRun(){return aE(this,_,"f")}currentMessageSnapshot(){return aE(this,U,"f")}currentRunStepSnapshot(){return aE(this,aa,"f")}async finalRunSteps(){return await this.done(),Object.values(aE(this,S,"f"))}async finalMessages(){return await this.done(),Object.values(aE(this,T,"f"))}async finalRun(){if(await this.done(),!aE(this,V,"f"))throw Error("Final run was not received.");return aE(this,V,"f")}async _createThreadAssistantStream(a,b,c){let d=c?.signal;d&&(d.aborted&&this.controller.abort(),d.addEventListener("abort",()=>this.controller.abort()));let e={...b,stream:!0},f=await a.createAndRun(e,{...c,signal:this.controller.signal});for await(let a of(this._connected(),f))aE(this,P,"m",ab).call(this,a);if(f.controller.signal?.aborted)throw new aK;return this._addRun(aE(this,P,"m",ac).call(this))}async _createAssistantStream(a,b,c,d){let e=d?.signal;e&&(e.aborted&&this.controller.abort(),e.addEventListener("abort",()=>this.controller.abort()));let f={...c,stream:!0},g=await a.create(b,f,{...d,signal:this.controller.signal});for await(let a of(this._connected(),g))aE(this,P,"m",ab).call(this,a);if(g.controller.signal?.aborted)throw new aK;return this._addRun(aE(this,P,"m",ac).call(this))}static accumulateDelta(a,b){for(let[c,d]of Object.entries(b)){if(!a.hasOwnProperty(c)){a[c]=d;continue}let b=a[c];if(null==b||"index"===c||"type"===c){a[c]=d;continue}if("string"==typeof b&&"string"==typeof d)b+=d;else if("number"==typeof b&&"number"==typeof d)b+=d;else if(a_(b)&&a_(d))b=this.accumulateDelta(b,d);else if(Array.isArray(b)&&Array.isArray(d)){if(b.every(a=>"string"==typeof a||"number"==typeof a)){b.push(...d);continue}for(let a of d){if(!a_(a))throw Error(`Expected array delta entry to be an object but got: ${a}`);let c=a.index;if(null==c)throw console.error(a),Error("Expected array delta entry to have an `index` property");if("number"!=typeof c)throw Error(`Expected array delta entry \`index\` property to be a number but got ${c}`);let d=b[c];null==d?b.push(a):b[c]=this.accumulateDelta(d,a)}continue}else throw Error(`Unhandled record type: ${c}, deltaValue: ${d}, accValue: ${b}`);a[c]=b}return a}_addRun(a){return a}async _threadAssistantStream(a,b,c){return await this._createThreadAssistantStream(b,a,c)}async _runAssistantStream(a,b,c,d){return await this._createAssistantStream(b,a,c,d)}async _runToolAssistantStream(a,b,c,d){return await this._createToolAssistantStream(b,a,c,d)}}Q=cr,ab=function(a){if(!this.ended)switch(aD(this,$,a,"f"),aE(this,P,"m",af).call(this,a),a.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":aE(this,P,"m",aj).call(this,a);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":aE(this,P,"m",ae).call(this,a);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":aE(this,P,"m",ad).call(this,a);break;case"error":throw Error("Encountered an error event in event processing - errors should be processed earlier")}},ac=function(){if(this.ended)throw new aI("stream has ended, this shouldn't happen");if(!aE(this,V,"f"))throw Error("Final run has not been received");return aE(this,V,"f")},ad=function(a){let[b,c]=aE(this,P,"m",ah).call(this,a,aE(this,U,"f"));for(let a of(aD(this,U,b,"f"),aE(this,T,"f")[b.id]=b,c)){let c=b.content[a.index];c?.type=="text"&&this._emit("textCreated",c.text)}switch(a.event){case"thread.message.created":this._emit("messageCreated",a.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",a.data.delta,b),a.data.delta.content)for(let c of a.data.delta.content){if("text"==c.type&&c.text){let a=c.text,d=b.content[c.index];if(d&&"text"==d.type)this._emit("textDelta",a,d.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(c.index!=aE(this,W,"f")){if(aE(this,X,"f"))switch(aE(this,X,"f").type){case"text":this._emit("textDone",aE(this,X,"f").text,aE(this,U,"f"));break;case"image_file":this._emit("imageFileDone",aE(this,X,"f").image_file,aE(this,U,"f"))}aD(this,W,c.index,"f")}aD(this,X,b.content[c.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(void 0!==aE(this,W,"f")){let b=a.data.content[aE(this,W,"f")];if(b)switch(b.type){case"image_file":this._emit("imageFileDone",b.image_file,aE(this,U,"f"));break;case"text":this._emit("textDone",b.text,aE(this,U,"f"))}}aE(this,U,"f")&&this._emit("messageDone",a.data),aD(this,U,void 0,"f")}},ae=function(a){let b=aE(this,P,"m",ag).call(this,a);switch(aD(this,aa,b,"f"),a.event){case"thread.run.step.created":this._emit("runStepCreated",a.data);break;case"thread.run.step.delta":let c=a.data.delta;if(c.step_details&&"tool_calls"==c.step_details.type&&c.step_details.tool_calls&&"tool_calls"==b.step_details.type)for(let a of c.step_details.tool_calls)a.index==aE(this,Y,"f")?this._emit("toolCallDelta",a,b.step_details.tool_calls[a.index]):(aE(this,Z,"f")&&this._emit("toolCallDone",aE(this,Z,"f")),aD(this,Y,a.index,"f"),aD(this,Z,b.step_details.tool_calls[a.index],"f"),aE(this,Z,"f")&&this._emit("toolCallCreated",aE(this,Z,"f")));this._emit("runStepDelta",a.data.delta,b);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":aD(this,aa,void 0,"f"),"tool_calls"==a.data.step_details.type&&aE(this,Z,"f")&&(this._emit("toolCallDone",aE(this,Z,"f")),aD(this,Z,void 0,"f")),this._emit("runStepDone",a.data,b)}},af=function(a){aE(this,R,"f").push(a),this._emit("event",a)},ag=function(a){switch(a.event){case"thread.run.step.created":return aE(this,S,"f")[a.data.id]=a.data,a.data;case"thread.run.step.delta":let b=aE(this,S,"f")[a.data.id];if(!b)throw Error("Received a RunStepDelta before creation of a snapshot");let c=a.data;if(c.delta){let d=Q.accumulateDelta(b,c.delta);aE(this,S,"f")[a.data.id]=d}return aE(this,S,"f")[a.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":aE(this,S,"f")[a.data.id]=a.data}if(aE(this,S,"f")[a.data.id])return aE(this,S,"f")[a.data.id];throw Error("No snapshot available")},ah=function(a,b){let c=[];switch(a.event){case"thread.message.created":return[a.data,c];case"thread.message.delta":if(!b)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let d=a.data;if(d.delta.content)for(let a of d.delta.content)if(a.index in b.content){let c=b.content[a.index];b.content[a.index]=aE(this,P,"m",ai).call(this,a,c)}else b.content[a.index]=a,c.push(a);return[b,c];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(b)return[b,c];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},ai=function(a,b){return Q.accumulateDelta(b,a)},aj=function(a){switch(aD(this,_,a.data,"f"),a.event){case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":case"thread.run.incomplete":aD(this,V,a.data,"f"),aE(this,Z,"f")&&(this._emit("toolCallDone",aE(this,Z,"f")),aD(this,Z,void 0,"f"))}};class cs extends bS{constructor(){super(...arguments),this.steps=new cp(this._client)}create(a,b,c){let{include:d,...e}=b;return this._client.post(bV`/threads/${a}/runs`,{query:{include:d},body:e,...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers]),stream:b.stream??!1})}retrieve(a,b,c){let{thread_id:d}=b;return this._client.get(bV`/threads/${d}/runs/${a}`,{...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}update(a,b,c){let{thread_id:d,...e}=b;return this._client.post(bV`/threads/${d}/runs/${a}`,{body:e,...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a,b={},c){return this._client.getAPIList(bV`/threads/${a}/runs`,bE,{query:b,...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}cancel(a,b,c){let{thread_id:d}=b;return this._client.post(bV`/threads/${d}/runs/${a}/cancel`,{...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}async createAndPoll(a,b,c){let d=await this.create(a,b,c);return await this.poll(d.id,{thread_id:a},c)}createAndStream(a,b,c){return cr.createAssistantStream(a,this._client.beta.threads.runs,b,c)}async poll(a,b,c){let d=ce([c?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":c?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:e,response:f}=await this.retrieve(a,b,{...c,headers:{...c?.headers,...d}}).withResponse();switch(e.status){case"queued":case"in_progress":case"cancelling":let g=5e3;if(c?.pollIntervalMs)g=c.pollIntervalMs;else{let a=f.headers.get("openai-poll-after-ms");if(a){let b=parseInt(a);isNaN(b)||(g=b)}}await a0(g);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return e}}}stream(a,b,c){return cr.createAssistantStream(a,this._client.beta.threads.runs,b,c)}submitToolOutputs(a,b,c){let{thread_id:d,...e}=b;return this._client.post(bV`/threads/${d}/runs/${a}/submit_tool_outputs`,{body:e,...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers]),stream:b.stream??!1})}async submitToolOutputsAndPoll(a,b,c){let d=await this.submitToolOutputs(a,b,c);return await this.poll(d.id,b,c)}submitToolOutputsStream(a,b,c){return cr.createToolAssistantStream(a,this._client.beta.threads.runs,b,c)}}cs.Steps=cp;class ct extends bS{constructor(){super(...arguments),this.runs=new cs(this._client),this.messages=new co(this._client)}create(a={},b){return this._client.post("/threads",{body:a,...b,headers:ce([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}retrieve(a,b){return this._client.get(bV`/threads/${a}`,{...b,headers:ce([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}update(a,b,c){return this._client.post(bV`/threads/${a}`,{body:b,...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}delete(a,b){return this._client.delete(bV`/threads/${a}`,{...b,headers:ce([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}createAndRun(a,b){return this._client.post("/threads/runs",{body:a,...b,headers:ce([{"OpenAI-Beta":"assistants=v2"},b?.headers]),stream:a.stream??!1})}async createAndRunPoll(a,b){let c=await this.createAndRun(a,b);return await this.runs.poll(c.id,{thread_id:c.thread_id},b)}createAndRunStream(a,b){return cr.createThreadAssistantStream(a,this._client.beta.threads,b)}}ct.Runs=cs,ct.Messages=co;class cu extends bS{constructor(){super(...arguments),this.realtime=new cn(this._client),this.assistants=new ck(this._client),this.threads=new ct(this._client)}}cu.Realtime=cn,cu.Assistants=ck,cu.Threads=ct;class cv extends bS{create(a,b){return this._client.post("/completions",{body:a,...b,stream:a.stream??!1})}}class cw extends bS{retrieve(a,b,c){let{container_id:d}=b;return this._client.get(bV`/containers/${d}/files/${a}/content`,{...c,headers:ce([{Accept:"application/binary"},c?.headers]),__binaryResponse:!0})}}class cx extends bS{constructor(){super(...arguments),this.content=new cw(this._client)}create(a,b,c){return this._client.post(bV`/containers/${a}/files`,bJ({body:b,...c},this._client))}retrieve(a,b,c){let{container_id:d}=b;return this._client.get(bV`/containers/${d}/files/${a}`,c)}list(a,b={},c){return this._client.getAPIList(bV`/containers/${a}/files`,bE,{query:b,...c})}delete(a,b,c){let{container_id:d}=b;return this._client.delete(bV`/containers/${d}/files/${a}`,{...c,headers:ce([{Accept:"*/*"},c?.headers])})}}cx.Content=cw;class cy extends bS{constructor(){super(...arguments),this.files=new cx(this._client)}create(a,b){return this._client.post("/containers",{body:a,...b})}retrieve(a,b){return this._client.get(bV`/containers/${a}`,b)}list(a={},b){return this._client.getAPIList("/containers",bE,{query:a,...b})}delete(a,b){return this._client.delete(bV`/containers/${a}`,{...b,headers:ce([{Accept:"*/*"},b?.headers])})}}cy.Files=cx;class cz extends bS{create(a,b){let c=!!a.encoding_format,d=c?a.encoding_format:"base64";c&&bs(this._client).debug("embeddings/user defined encoding_format:",a.encoding_format);let e=this._client.post("/embeddings",{body:{...a,encoding_format:d},...b});return c?e:(bs(this._client).debug("embeddings/decoding base64 embeddings from base64"),e._thenUnwrap(a=>(a&&a.data&&a.data.forEach(a=>{let b=a.embedding;a.embedding=(a=>{if("undefined"!=typeof Buffer){let b=Buffer.from(a,"base64");return Array.from(new Float32Array(b.buffer,b.byteOffset,b.length/Float32Array.BYTES_PER_ELEMENT))}{let b=atob(a),c=b.length,d=new Uint8Array(c);for(let a=0;a<c;a++)d[a]=b.charCodeAt(a);return Array.from(new Float32Array(d.buffer))}})(b)}),a)))}}class cA extends bS{retrieve(a,b,c){let{eval_id:d,run_id:e}=b;return this._client.get(bV`/evals/${d}/runs/${e}/output_items/${a}`,c)}list(a,b,c){let{eval_id:d,...e}=b;return this._client.getAPIList(bV`/evals/${d}/runs/${a}/output_items`,bE,{query:e,...c})}}class cB extends bS{constructor(){super(...arguments),this.outputItems=new cA(this._client)}create(a,b,c){return this._client.post(bV`/evals/${a}/runs`,{body:b,...c})}retrieve(a,b,c){let{eval_id:d}=b;return this._client.get(bV`/evals/${d}/runs/${a}`,c)}list(a,b={},c){return this._client.getAPIList(bV`/evals/${a}/runs`,bE,{query:b,...c})}delete(a,b,c){let{eval_id:d}=b;return this._client.delete(bV`/evals/${d}/runs/${a}`,c)}cancel(a,b,c){let{eval_id:d}=b;return this._client.post(bV`/evals/${d}/runs/${a}`,c)}}cB.OutputItems=cA;class cC extends bS{constructor(){super(...arguments),this.runs=new cB(this._client)}create(a,b){return this._client.post("/evals",{body:a,...b})}retrieve(a,b){return this._client.get(bV`/evals/${a}`,b)}update(a,b,c){return this._client.post(bV`/evals/${a}`,{body:b,...c})}list(a={},b){return this._client.getAPIList("/evals",bE,{query:a,...b})}delete(a,b){return this._client.delete(bV`/evals/${a}`,b)}}cC.Runs=cB;class cD extends bS{create(a,b){return this._client.post("/files",bJ({body:a,...b},this._client))}retrieve(a,b){return this._client.get(bV`/files/${a}`,b)}list(a={},b){return this._client.getAPIList("/files",bE,{query:a,...b})}delete(a,b){return this._client.delete(bV`/files/${a}`,b)}content(a,b){return this._client.get(bV`/files/${a}/content`,{...b,headers:ce([{Accept:"application/binary"},b?.headers]),__binaryResponse:!0})}async waitForProcessing(a,{pollInterval:b=5e3,maxWait:c=18e5}={}){let d=new Set(["processed","error","deleted"]),e=Date.now(),f=await this.retrieve(a);for(;!f.status||!d.has(f.status);)if(await a0(b),f=await this.retrieve(a),Date.now()-e>c)throw new aM({message:`Giving up on waiting for file ${a} to finish processing after ${c} milliseconds.`});return f}}class cE extends bS{}class cF extends bS{run(a,b){return this._client.post("/fine_tuning/alpha/graders/run",{body:a,...b})}validate(a,b){return this._client.post("/fine_tuning/alpha/graders/validate",{body:a,...b})}}class cG extends bS{constructor(){super(...arguments),this.graders=new cF(this._client)}}cG.Graders=cF;class cH extends bS{create(a,b,c){return this._client.getAPIList(bV`/fine_tuning/checkpoints/${a}/permissions`,bD,{body:b,method:"post",...c})}retrieve(a,b={},c){return this._client.get(bV`/fine_tuning/checkpoints/${a}/permissions`,{query:b,...c})}delete(a,b,c){let{fine_tuned_model_checkpoint:d}=b;return this._client.delete(bV`/fine_tuning/checkpoints/${d}/permissions/${a}`,c)}}class cI extends bS{constructor(){super(...arguments),this.permissions=new cH(this._client)}}cI.Permissions=cH;class cJ extends bS{list(a,b={},c){return this._client.getAPIList(bV`/fine_tuning/jobs/${a}/checkpoints`,bE,{query:b,...c})}}class cK extends bS{constructor(){super(...arguments),this.checkpoints=new cJ(this._client)}create(a,b){return this._client.post("/fine_tuning/jobs",{body:a,...b})}retrieve(a,b){return this._client.get(bV`/fine_tuning/jobs/${a}`,b)}list(a={},b){return this._client.getAPIList("/fine_tuning/jobs",bE,{query:a,...b})}cancel(a,b){return this._client.post(bV`/fine_tuning/jobs/${a}/cancel`,b)}listEvents(a,b={},c){return this._client.getAPIList(bV`/fine_tuning/jobs/${a}/events`,bE,{query:b,...c})}pause(a,b){return this._client.post(bV`/fine_tuning/jobs/${a}/pause`,b)}resume(a,b){return this._client.post(bV`/fine_tuning/jobs/${a}/resume`,b)}}cK.Checkpoints=cJ;class cL extends bS{constructor(){super(...arguments),this.methods=new cE(this._client),this.jobs=new cK(this._client),this.checkpoints=new cI(this._client),this.alpha=new cG(this._client)}}cL.Methods=cE,cL.Jobs=cK,cL.Checkpoints=cI,cL.Alpha=cG;class cM extends bS{}class cN extends bS{constructor(){super(...arguments),this.graderModels=new cM(this._client)}}cN.GraderModels=cM;class cO extends bS{createVariation(a,b){return this._client.post("/images/variations",bJ({body:a,...b},this._client))}edit(a,b){return this._client.post("/images/edits",bJ({body:a,...b,stream:a.stream??!1},this._client))}generate(a,b){return this._client.post("/images/generations",{body:a,...b,stream:a.stream??!1})}}class cP extends bS{retrieve(a,b){return this._client.get(bV`/models/${a}`,b)}list(a){return this._client.getAPIList("/models",bD,a)}delete(a,b){return this._client.delete(bV`/models/${a}`,b)}}class cQ extends bS{create(a,b){return this._client.post("/moderations",{body:a,...b})}}function cR(a,b){let c=a.output.map(a=>{if("function_call"===a.type)return{...a,parsed_arguments:function(a,b){var c,d;let e=(c=a.tools??[],d=b.name,c.find(a=>"function"===a.type&&a.name===d));return{...b,...b,parsed_arguments:e?.$brand==="auto-parseable-tool"?e.$parseRaw(b.arguments):e?.strict?JSON.parse(b.arguments):null}}(b,a)};if("message"===a.type){let c=a.content.map(a=>{var c,d;return"output_text"===a.type?{...a,parsed:(c=b,d=a.text,c.text?.format?.type!=="json_schema"?null:"$parseRaw"in c.text?.format?(c.text?.format).$parseRaw(d):JSON.parse(d))}:a});return{...a,content:c}}return a}),d=Object.assign({},a,{output:c});return Object.getOwnPropertyDescriptor(a,"output_text")||cS(d),Object.defineProperty(d,"output_parsed",{enumerable:!0,get(){for(let a of d.output)if("message"===a.type){for(let b of a.content)if("output_text"===b.type&&null!==b.parsed)return b.parsed}return null}}),d}function cS(a){let b=[];for(let c of a.output)if("message"===c.type)for(let a of c.content)"output_text"===a.type&&b.push(a.text);a.output_text=b.join("")}class cT extends bZ{constructor(a){super(),ak.add(this),al.set(this,void 0),am.set(this,void 0),an.set(this,void 0),aD(this,al,a,"f")}static createResponse(a,b,c){let d=new cT(b);return d._run(()=>d._createOrRetrieveResponse(a,b,{...c,headers:{...c?.headers,"X-Stainless-Helper-Method":"stream"}})),d}async _createOrRetrieveResponse(a,b,c){let d,e=c?.signal;e&&(e.aborted&&this.controller.abort(),e.addEventListener("abort",()=>this.controller.abort())),aE(this,ak,"m",ao).call(this);let f=null;for await(let e of("response_id"in b?(d=await a.responses.retrieve(b.response_id,{stream:!0},{...c,signal:this.controller.signal,stream:!0}),f=b.starting_after??null):d=await a.responses.create({...b,stream:!0},{...c,signal:this.controller.signal}),this._connected(),d))aE(this,ak,"m",ap).call(this,e,f);if(d.controller.signal?.aborted)throw new aK;return aE(this,ak,"m",aq).call(this)}[(al=new WeakMap,am=new WeakMap,an=new WeakMap,ak=new WeakSet,ao=function(){this.ended||aD(this,am,void 0,"f")},ap=function(a,b){if(this.ended)return;let c=(a,c)=>{(null==b||c.sequence_number>b)&&this._emit(a,c)},d=aE(this,ak,"m",ar).call(this,a);switch(c("event",a),a.type){case"response.output_text.delta":{let b=d.output[a.output_index];if(!b)throw new aI(`missing output at index ${a.output_index}`);if("message"===b.type){let d=b.content[a.content_index];if(!d)throw new aI(`missing content at index ${a.content_index}`);if("output_text"!==d.type)throw new aI(`expected content to be 'output_text', got ${d.type}`);c("response.output_text.delta",{...a,snapshot:d.text})}break}case"response.function_call_arguments.delta":{let b=d.output[a.output_index];if(!b)throw new aI(`missing output at index ${a.output_index}`);"function_call"===b.type&&c("response.function_call_arguments.delta",{...a,snapshot:b.arguments});break}default:c(a.type,a)}},aq=function(){if(this.ended)throw new aI("stream has ended, this shouldn't happen");let a=aE(this,am,"f");if(!a)throw new aI("request ended without sending any events");aD(this,am,void 0,"f");let b=function(a,b){var c;return b&&(c=b,b$(c.text?.format))?cR(a,b):{...a,output_parsed:null,output:a.output.map(a=>"function_call"===a.type?{...a,parsed_arguments:null}:"message"===a.type?{...a,content:a.content.map(a=>({...a,parsed:null}))}:a)}}(a,aE(this,al,"f"));return aD(this,an,b,"f"),b},ar=function(a){let b=aE(this,am,"f");if(!b){if("response.created"!==a.type)throw new aI(`When snapshot hasn't been set yet, expected 'response.created' event, got ${a.type}`);return aD(this,am,a.response,"f")}switch(a.type){case"response.output_item.added":b.output.push(a.item);break;case"response.content_part.added":{let c=b.output[a.output_index];if(!c)throw new aI(`missing output at index ${a.output_index}`);"message"===c.type&&c.content.push(a.part);break}case"response.output_text.delta":{let c=b.output[a.output_index];if(!c)throw new aI(`missing output at index ${a.output_index}`);if("message"===c.type){let b=c.content[a.content_index];if(!b)throw new aI(`missing content at index ${a.content_index}`);if("output_text"!==b.type)throw new aI(`expected content to be 'output_text', got ${b.type}`);b.text+=a.delta}break}case"response.function_call_arguments.delta":{let c=b.output[a.output_index];if(!c)throw new aI(`missing output at index ${a.output_index}`);"function_call"===c.type&&(c.arguments+=a.delta);break}case"response.completed":aD(this,am,a.response,"f")}return b},Symbol.asyncIterator)](){let a=[],b=[],c=!1;return this.on("event",c=>{let d=b.shift();d?d.resolve(c):a.push(c)}),this.on("end",()=>{for(let a of(c=!0,b))a.resolve(void 0);b.length=0}),this.on("abort",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),this.on("error",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),{next:async()=>a.length?{value:a.shift(),done:!1}:c?{value:void 0,done:!0}:new Promise((a,c)=>b.push({resolve:a,reject:c})).then(a=>a?{value:a,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();let a=aE(this,an,"f");if(!a)throw new aI("stream ended without producing a ChatCompletion");return a}}class cU extends bS{list(a,b={},c){return this._client.getAPIList(bV`/responses/${a}/input_items`,bE,{query:b,...c})}}class cV extends bS{constructor(){super(...arguments),this.inputItems=new cU(this._client)}create(a,b){return this._client.post("/responses",{body:a,...b,stream:a.stream??!1})._thenUnwrap(a=>("object"in a&&"response"===a.object&&cS(a),a))}retrieve(a,b={},c){return this._client.get(bV`/responses/${a}`,{query:b,...c,stream:b?.stream??!1})._thenUnwrap(a=>("object"in a&&"response"===a.object&&cS(a),a))}delete(a,b){return this._client.delete(bV`/responses/${a}`,{...b,headers:ce([{Accept:"*/*"},b?.headers])})}parse(a,b){return this._client.responses.create(a,b)._thenUnwrap(b=>cR(b,a))}stream(a,b){return cT.createResponse(this._client,a,b)}cancel(a,b){return this._client.post(bV`/responses/${a}/cancel`,b)}}cV.InputItems=cU;class cW extends bS{create(a,b,c){return this._client.post(bV`/uploads/${a}/parts`,bJ({body:b,...c},this._client))}}class cX extends bS{constructor(){super(...arguments),this.parts=new cW(this._client)}create(a,b){return this._client.post("/uploads",{body:a,...b})}cancel(a,b){return this._client.post(bV`/uploads/${a}/cancel`,b)}complete(a,b,c){return this._client.post(bV`/uploads/${a}/complete`,{body:b,...c})}}cX.Parts=cW;let cY=async a=>{let b=await Promise.allSettled(a),c=b.filter(a=>"rejected"===a.status);if(c.length){for(let a of c)console.error(a.reason);throw Error(`${c.length} promise(s) failed - see the above errors`)}let d=[];for(let a of b)"fulfilled"===a.status&&d.push(a.value);return d};class cZ extends bS{create(a,b,c){return this._client.post(bV`/vector_stores/${a}/file_batches`,{body:b,...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}retrieve(a,b,c){let{vector_store_id:d}=b;return this._client.get(bV`/vector_stores/${d}/file_batches/${a}`,{...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}cancel(a,b,c){let{vector_store_id:d}=b;return this._client.post(bV`/vector_stores/${d}/file_batches/${a}/cancel`,{...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}async createAndPoll(a,b,c){let d=await this.create(a,b);return await this.poll(a,d.id,c)}listFiles(a,b,c){let{vector_store_id:d,...e}=b;return this._client.getAPIList(bV`/vector_stores/${d}/file_batches/${a}/files`,bE,{query:e,...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}async poll(a,b,c){let d=ce([c?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":c?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:e,response:f}=await this.retrieve(b,{vector_store_id:a},{...c,headers:d}).withResponse();switch(e.status){case"in_progress":let g=5e3;if(c?.pollIntervalMs)g=c.pollIntervalMs;else{let a=f.headers.get("openai-poll-after-ms");if(a){let b=parseInt(a);isNaN(b)||(g=b)}}await a0(g);break;case"failed":case"cancelled":case"completed":return e}}}async uploadAndPoll(a,{files:b,fileIds:c=[]},d){if(null==b||0==b.length)throw Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");let e=Math.min(d?.maxConcurrency??5,b.length),f=this._client,g=b.values(),h=[...c];async function i(a){for(let b of a){let a=await f.files.create({file:b,purpose:"assistants"},d);h.push(a.id)}}let j=Array(e).fill(g).map(i);return await cY(j),await this.createAndPoll(a,{file_ids:h})}}class c$ extends bS{create(a,b,c){return this._client.post(bV`/vector_stores/${a}/files`,{body:b,...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}retrieve(a,b,c){let{vector_store_id:d}=b;return this._client.get(bV`/vector_stores/${d}/files/${a}`,{...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}update(a,b,c){let{vector_store_id:d,...e}=b;return this._client.post(bV`/vector_stores/${d}/files/${a}`,{body:e,...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a,b={},c){return this._client.getAPIList(bV`/vector_stores/${a}/files`,bE,{query:b,...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}delete(a,b,c){let{vector_store_id:d}=b;return this._client.delete(bV`/vector_stores/${d}/files/${a}`,{...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}async createAndPoll(a,b,c){let d=await this.create(a,b,c);return await this.poll(a,d.id,c)}async poll(a,b,c){let d=ce([c?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":c?.pollIntervalMs?.toString()??void 0}]);for(;;){let e=await this.retrieve(b,{vector_store_id:a},{...c,headers:d}).withResponse(),f=e.data;switch(f.status){case"in_progress":let g=5e3;if(c?.pollIntervalMs)g=c.pollIntervalMs;else{let a=e.response.headers.get("openai-poll-after-ms");if(a){let b=parseInt(a);isNaN(b)||(g=b)}}await a0(g);break;case"failed":case"completed":return f}}}async upload(a,b,c){let d=await this._client.files.create({file:b,purpose:"assistants"},c);return this.create(a,{file_id:d.id},c)}async uploadAndPoll(a,b,c){let d=await this.upload(a,b,c);return await this.poll(a,d.id,c)}content(a,b,c){let{vector_store_id:d}=b;return this._client.getAPIList(bV`/vector_stores/${d}/files/${a}/content`,bD,{...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}}class c_ extends bS{constructor(){super(...arguments),this.files=new c$(this._client),this.fileBatches=new cZ(this._client)}create(a,b){return this._client.post("/vector_stores",{body:a,...b,headers:ce([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}retrieve(a,b){return this._client.get(bV`/vector_stores/${a}`,{...b,headers:ce([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}update(a,b,c){return this._client.post(bV`/vector_stores/${a}`,{body:b,...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a={},b){return this._client.getAPIList("/vector_stores",bE,{query:a,...b,headers:ce([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}delete(a,b){return this._client.delete(bV`/vector_stores/${a}`,{...b,headers:ce([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}search(a,b,c){return this._client.getAPIList(bV`/vector_stores/${a}/search`,bD,{body:b,method:"post",...c,headers:ce([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}}c_.Files=c$,c_.FileBatches=cZ;class c0 extends bS{constructor(){super(...arguments),as.add(this)}async unwrap(a,b,c=this._client.webhookSecret,d=300){return await this.verifySignature(a,b,c,d),JSON.parse(a)}async verifySignature(a,b,c=this._client.webhookSecret,d=300){if("undefined"==typeof crypto||"function"!=typeof crypto.subtle.importKey||"function"!=typeof crypto.subtle.verify)throw Error("Webhook signature verification is only supported when the `crypto` global is defined");aE(this,as,"m",at).call(this,c);let e=ce([b]).values,f=aE(this,as,"m",au).call(this,e,"webhook-signature"),g=aE(this,as,"m",au).call(this,e,"webhook-timestamp"),h=aE(this,as,"m",au).call(this,e,"webhook-id"),i=parseInt(g,10);if(isNaN(i))throw new aX("Invalid webhook timestamp format");let j=Math.floor(Date.now()/1e3);if(j-i>d)throw new aX("Webhook timestamp is too old");if(i>j+d)throw new aX("Webhook timestamp is too new");let k=f.split(" ").map(a=>a.startsWith("v1,")?a.substring(3):a),l=c.startsWith("whsec_")?Buffer.from(c.replace("whsec_",""),"base64"):Buffer.from(c,"utf-8"),m=h?`${h}.${g}.${a}`:`${g}.${a}`,n=await crypto.subtle.importKey("raw",l,{name:"HMAC",hash:"SHA-256"},!1,["verify"]);for(let a of k)try{let b=Buffer.from(a,"base64");if(await crypto.subtle.verify("HMAC",n,b,new TextEncoder().encode(m)))return}catch{continue}throw new aX("The given webhook signature does not match the expected signature")}}as=new WeakSet,at=function(a){if("string"!=typeof a||0===a.length)throw Error("The webhook secret must either be set using the env var, OPENAI_WEBHOOK_SECRET, on the client class, OpenAI({ webhookSecret: '123' }), or passed to this function")},au=function(a,b){if(!a)throw Error("Headers are required");let c=a.get(b);if(null==c)throw Error(`Missing required header: ${b}`);return c};class c1{constructor({baseURL:a=cq("OPENAI_BASE_URL"),apiKey:b=cq("OPENAI_API_KEY"),organization:c=cq("OPENAI_ORG_ID")??null,project:d=cq("OPENAI_PROJECT_ID")??null,webhookSecret:e=cq("OPENAI_WEBHOOK_SECRET")??null,...f}={}){if(av.add(this),ax.set(this,void 0),this.completions=new cv(this),this.chat=new cc(this),this.embeddings=new cz(this),this.files=new cD(this),this.images=new cO(this),this.audio=new ci(this),this.moderations=new cQ(this),this.models=new cP(this),this.fineTuning=new cL(this),this.graders=new cN(this),this.vectorStores=new c_(this),this.webhooks=new c0(this),this.beta=new cu(this),this.batches=new cj(this),this.uploads=new cX(this),this.responses=new cV(this),this.evals=new cC(this),this.containers=new cy(this),void 0===b)throw new aI("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");let g={apiKey:b,organization:c,project:d,webhookSecret:e,...f,baseURL:a||"https://api.openai.com/v1"};if(!g.dangerouslyAllowBrowser&&"undefined"!=typeof window&&void 0!==window.document&&"undefined"!=typeof navigator)throw new aI("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew OpenAI({ apiKey, dangerouslyAllowBrowser: true });\n\nhttps://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety\n");this.baseURL=g.baseURL,this.timeout=g.timeout??aw.DEFAULT_TIMEOUT,this.logger=g.logger??console;let h="warn";this.logLevel=h,this.logLevel=bn(g.logLevel,"ClientOptions.logLevel",this)??bn(cq("OPENAI_LOG"),"process.env['OPENAI_LOG']",this)??h,this.fetchOptions=g.fetchOptions,this.maxRetries=g.maxRetries??2,this.fetch=g.fetch??function(){if("undefined"!=typeof fetch)return fetch;throw Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new OpenAI({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}(),aD(this,ax,a8,"f"),this._options=g,this.apiKey=b,this.organization=c,this.project=d,this.webhookSecret=e}withOptions(a){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetch:this.fetch,fetchOptions:this.fetchOptions,apiKey:this.apiKey,organization:this.organization,project:this.project,webhookSecret:this.webhookSecret,...a})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:a,nulls:b}){}async authHeaders(a){return ce([{Authorization:`Bearer ${this.apiKey}`}])}stringifyQuery(a){return function(a,b={}){let c,d=a,e=function(a=bh){let b;if(void 0!==a.allowEmptyArrays&&"boolean"!=typeof a.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==a.encodeDotInKeys&&"boolean"!=typeof a.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==a.encoder&&void 0!==a.encoder&&"function"!=typeof a.encoder)throw TypeError("Encoder has to be a function.");let c=a.charset||bh.charset;if(void 0!==a.charset&&"utf-8"!==a.charset&&"iso-8859-1"!==a.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let d=a9;if(void 0!==a.format){if(!bc(bb,a.format))throw TypeError("Unknown format option provided.");d=a.format}let e=bb[d],f=bh.filter;if(("function"==typeof a.filter||aZ(a.filter))&&(f=a.filter),b=a.arrayFormat&&a.arrayFormat in bf?a.arrayFormat:"indices"in a?a.indices?"indices":"repeat":bh.arrayFormat,"commaRoundTrip"in a&&"boolean"!=typeof a.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");let g=void 0===a.allowDots?!0==!!a.encodeDotInKeys||bh.allowDots:!!a.allowDots;return{addQueryPrefix:"boolean"==typeof a.addQueryPrefix?a.addQueryPrefix:bh.addQueryPrefix,allowDots:g,allowEmptyArrays:"boolean"==typeof a.allowEmptyArrays?!!a.allowEmptyArrays:bh.allowEmptyArrays,arrayFormat:b,charset:c,charsetSentinel:"boolean"==typeof a.charsetSentinel?a.charsetSentinel:bh.charsetSentinel,commaRoundTrip:!!a.commaRoundTrip,delimiter:void 0===a.delimiter?bh.delimiter:a.delimiter,encode:"boolean"==typeof a.encode?a.encode:bh.encode,encodeDotInKeys:"boolean"==typeof a.encodeDotInKeys?a.encodeDotInKeys:bh.encodeDotInKeys,encoder:"function"==typeof a.encoder?a.encoder:bh.encoder,encodeValuesOnly:"boolean"==typeof a.encodeValuesOnly?a.encodeValuesOnly:bh.encodeValuesOnly,filter:f,format:d,formatter:e,serializeDate:"function"==typeof a.serializeDate?a.serializeDate:bh.serializeDate,skipNulls:"boolean"==typeof a.skipNulls?a.skipNulls:bh.skipNulls,sort:"function"==typeof a.sort?a.sort:null,strictNullHandling:"boolean"==typeof a.strictNullHandling?a.strictNullHandling:bh.strictNullHandling}}(b);"function"==typeof e.filter?d=(0,e.filter)("",d):aZ(e.filter)&&(c=e.filter);let f=[];if("object"!=typeof d||null===d)return"";let g=bf[e.arrayFormat],h="comma"===g&&e.commaRoundTrip;c||(c=Object.keys(d)),e.sort&&c.sort(e.sort);let i=new WeakMap;for(let a=0;a<c.length;++a){let b=c[a];e.skipNulls&&null===d[b]||bg(f,function a(b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s){var t,u;let v,w=b,x=s,y=0,z=!1;for(;void 0!==(x=x.get(bi))&&!z;){let a=x.get(b);if(y+=1,void 0!==a)if(a===y)throw RangeError("Cyclic object value");else z=!0;void 0===x.get(bi)&&(y=0)}if("function"==typeof k?w=k(c,w):w instanceof Date?w=n?.(w):"comma"===d&&aZ(w)&&(w=be(w,function(a){return a instanceof Date?n?.(a):a})),null===w){if(g)return j&&!q?j(c,bh.encoder,r,"key",o):c;w=""}if("string"==typeof(t=w)||"number"==typeof t||"boolean"==typeof t||"symbol"==typeof t||"bigint"==typeof t||(u=w)&&"object"==typeof u&&u.constructor&&u.constructor.isBuffer&&u.constructor.isBuffer(u)){if(j){let a=q?c:j(c,bh.encoder,r,"key",o);return[p?.(a)+"="+p?.(j(w,bh.encoder,r,"value",o))]}return[p?.(c)+"="+p?.(String(w))]}let A=[];if(void 0===w)return A;if("comma"===d&&aZ(w))q&&j&&(w=be(w,j)),v=[{value:w.length>0?w.join(",")||null:void 0}];else if(aZ(k))v=k;else{let a=Object.keys(w);v=l?a.sort(l):a}let B=i?String(c).replace(/\./g,"%2E"):String(c),C=e&&aZ(w)&&1===w.length?B+"[]":B;if(f&&aZ(w)&&0===w.length)return C+"[]";for(let c=0;c<v.length;++c){let t=v[c],u="object"==typeof t&&void 0!==t.value?t.value:w[t];if(h&&null===u)continue;let x=m&&i?t.replace(/\./g,"%2E"):t,z=aZ(w)?"function"==typeof d?d(C,x):C:C+(m?"."+x:"["+x+"]");s.set(b,y);let B=new WeakMap;B.set(bi,s),bg(A,a(u,z,d,e,f,g,h,i,"comma"===d&&q&&aZ(w)?null:j,k,l,m,n,o,p,q,r,B))}return A}(d[b],b,g,h,e.allowEmptyArrays,e.strictNullHandling,e.skipNulls,e.encodeDotInKeys,e.encode?e.encoder:null,e.filter,e.sort,e.allowDots,e.serializeDate,e.format,e.formatter,e.encodeValuesOnly,e.charset,i))}let j=f.join(e.delimiter),k=!0===e.addQueryPrefix?"?":"";return e.charsetSentinel&&("iso-8859-1"===e.charset?k+="utf8=%26%2310003%3B&":k+="utf8=%E2%9C%93&"),j.length>0?k+j:""}(a,{arrayFormat:"brackets"})}getUserAgent(){return`${this.constructor.name}/JS ${a1}`}defaultIdempotencyKey(){return`stainless-node-retry-${aF()}`}makeStatusError(a,b,c,d){return aJ.generate(a,b,c,d)}buildURL(a,b,c){let d=!aE(this,av,"m",ay).call(this)&&c||this.baseURL,e=new URL(aY.test(a)?a:d+(d.endsWith("/")&&a.startsWith("/")?a.slice(1):a)),f=this.defaultQuery();return!function(a){if(!a)return!0;for(let b in a)return!1;return!0}(f)&&(b={...f,...b}),"object"==typeof b&&b&&!Array.isArray(b)&&(e.search=this.stringifyQuery(b)),e.toString()}async prepareOptions(a){}async prepareRequest(a,{url:b,options:c}){}get(a,b){return this.methodRequest("get",a,b)}post(a,b){return this.methodRequest("post",a,b)}patch(a,b){return this.methodRequest("patch",a,b)}put(a,b){return this.methodRequest("put",a,b)}delete(a,b){return this.methodRequest("delete",a,b)}methodRequest(a,b,c){return this.request(Promise.resolve(c).then(c=>({method:a,path:b,...c})))}request(a,b=null){return new bA(this,this.makeRequest(a,b,void 0))}async makeRequest(a,b,c){let d=await a,e=d.maxRetries??this.maxRetries;null==b&&(b=e),await this.prepareOptions(d);let{req:f,url:g,timeout:h}=await this.buildRequest(d,{retryCount:e-b});await this.prepareRequest(f,{url:g,options:d});let i="log_"+(0x1000000*Math.random()|0).toString(16).padStart(6,"0"),j=void 0===c?"":`, retryOf: ${c}`,k=Date.now();if(bs(this).debug(`[${i}] sending request`,bt({retryOfRequestLogID:c,method:d.method,url:g,options:d,headers:f.headers})),d.signal?.aborted)throw new aK;let l=new AbortController,m=await this.fetchWithTimeout(g,f,h,l).catch(aH),n=Date.now();if(m instanceof Error){let a=`retrying, ${b} attempts remaining`;if(d.signal?.aborted)throw new aK;let e=aG(m)||/timed? ?out/i.test(String(m)+("cause"in m?String(m.cause):""));if(b)return bs(this).info(`[${i}] connection ${e?"timed out":"failed"} - ${a}`),bs(this).debug(`[${i}] connection ${e?"timed out":"failed"} (${a})`,bt({retryOfRequestLogID:c,url:g,durationMs:n-k,message:m.message})),this.retryRequest(d,b,c??i);if(bs(this).info(`[${i}] connection ${e?"timed out":"failed"} - error; no more retries left`),bs(this).debug(`[${i}] connection ${e?"timed out":"failed"} (error; no more retries left)`,bt({retryOfRequestLogID:c,url:g,durationMs:n-k,message:m.message})),e)throw new aM;throw new aL({cause:m})}let o=[...m.headers.entries()].filter(([a])=>"x-request-id"===a).map(([a,b])=>", "+a+": "+JSON.stringify(b)).join(""),p=`[${i}${j}${o}] ${f.method} ${g} ${m.ok?"succeeded":"failed"} with status ${m.status} in ${n-k}ms`;if(!m.ok){let a=await this.shouldRetry(m);if(b&&a){let a=`retrying, ${b} attempts remaining`;return await a7(m.body),bs(this).info(`${p} - ${a}`),bs(this).debug(`[${i}] response error (${a})`,bt({retryOfRequestLogID:c,url:m.url,status:m.status,headers:m.headers,durationMs:n-k})),this.retryRequest(d,b,c??i,m.headers)}let e=a?"error; no more retries left":"error; not retryable";bs(this).info(`${p} - ${e}`);let f=await m.text().catch(a=>aH(a).message),g=(a=>{try{return JSON.parse(a)}catch(a){return}})(f),h=g?void 0:f;throw bs(this).debug(`[${i}] response error (${e})`,bt({retryOfRequestLogID:c,url:m.url,status:m.status,headers:m.headers,message:h,durationMs:Date.now()-k})),this.makeStatusError(m.status,g,h,m.headers)}return bs(this).info(p),bs(this).debug(`[${i}] response start`,bt({retryOfRequestLogID:c,url:m.url,status:m.status,headers:m.headers,durationMs:n-k})),{response:m,options:d,controller:l,requestLogID:i,retryOfRequestLogID:c,startTime:k}}getAPIList(a,b,c){return this.requestAPIList(b,{method:"get",path:a,...c})}requestAPIList(a,b){return new bC(this,this.makeRequest(b,null,void 0),a)}async fetchWithTimeout(a,b,c,d){let{signal:e,method:f,...g}=b||{};e&&e.addEventListener("abort",()=>d.abort());let h=setTimeout(()=>d.abort(),c),i=globalThis.ReadableStream&&g.body instanceof globalThis.ReadableStream||"object"==typeof g.body&&null!==g.body&&Symbol.asyncIterator in g.body,j={signal:d.signal,...i?{duplex:"half"}:{},method:"GET",...g};f&&(j.method=f.toUpperCase());try{return await this.fetch.call(void 0,a,j)}finally{clearTimeout(h)}}async shouldRetry(a){let b=a.headers.get("x-should-retry");return"true"===b||"false"!==b&&(408===a.status||409===a.status||429===a.status||!!(a.status>=500))}async retryRequest(a,b,c,d){let e,f=d?.get("retry-after-ms");if(f){let a=parseFloat(f);Number.isNaN(a)||(e=a)}let g=d?.get("retry-after");if(g&&!e){let a=parseFloat(g);e=Number.isNaN(a)?Date.parse(g)-Date.now():1e3*a}if(!(e&&0<=e&&e<6e4)){let c=a.maxRetries??this.maxRetries;e=this.calculateDefaultRetryTimeoutMillis(b,c)}return await a0(e),this.makeRequest(a,b-1,c)}calculateDefaultRetryTimeoutMillis(a,b){return Math.min(.5*Math.pow(2,b-a),8)*(1-.25*Math.random())*1e3}async buildRequest(a,{retryCount:b=0}={}){let c={...a},{method:d,path:e,query:f,defaultBaseURL:g}=c,h=this.buildURL(e,f,g);"timeout"in c&&((a,b)=>{if("number"!=typeof b||!Number.isInteger(b))throw new aI(`${a} must be an integer`);if(b<0)throw new aI(`${a} must be a positive integer`)})("timeout",c.timeout),c.timeout=c.timeout??this.timeout;let{bodyHeaders:i,body:j}=this.buildBody({options:c}),k=await this.buildHeaders({options:a,method:d,bodyHeaders:i,retryCount:b});return{req:{method:d,headers:k,...c.signal&&{signal:c.signal},...globalThis.ReadableStream&&j instanceof globalThis.ReadableStream&&{duplex:"half"},...j&&{body:j},...this.fetchOptions??{},...c.fetchOptions??{}},url:h,timeout:c.timeout}}async buildHeaders({options:a,method:b,bodyHeaders:c,retryCount:d}){let e={};this.idempotencyHeader&&"get"!==b&&(a.idempotencyKey||(a.idempotencyKey=this.defaultIdempotencyKey()),e[this.idempotencyHeader]=a.idempotencyKey);let f=ce([e,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(d),...a.timeout?{"X-Stainless-Timeout":String(Math.trunc(a.timeout/1e3))}:{},...az??(az=(()=>{let a="undefined"!=typeof Deno&&null!=Deno.build?"deno":"undefined"!=typeof EdgeRuntime?"edge":"[object process]"===Object.prototype.toString.call(void 0!==globalThis.process?globalThis.process:0)?"node":"unknown";if("deno"===a)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":a1,"X-Stainless-OS":a3(Deno.build.os),"X-Stainless-Arch":a2(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":a1,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if("node"===a)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":a1,"X-Stainless-OS":a3(globalThis.process.platform??"unknown"),"X-Stainless-Arch":a2(globalThis.process.arch??"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version??"unknown"};let b=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:a,pattern:b}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let c=b.exec(navigator.userAgent);if(c){let b=c[1]||0,d=c[2]||0,e=c[3]||0;return{browser:a,version:`${b}.${d}.${e}`}}}return null}();return b?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":a1,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${b.browser}`,"X-Stainless-Runtime-Version":b.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":a1,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}})()),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project},await this.authHeaders(a),this._options.defaultHeaders,c,a.headers]);return this.validateHeaders(f),f.values}buildBody({options:{body:a,headers:b}}){if(!a)return{bodyHeaders:void 0,body:void 0};let c=ce([b]);return ArrayBuffer.isView(a)||a instanceof ArrayBuffer||a instanceof DataView||"string"==typeof a&&c.values.has("content-type")||a instanceof Blob||a instanceof FormData||a instanceof URLSearchParams||globalThis.ReadableStream&&a instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:a}:"object"==typeof a&&(Symbol.asyncIterator in a||Symbol.iterator in a&&"next"in a&&"function"==typeof a.next)?{bodyHeaders:void 0,body:a5(a)}:aE(this,ax,"f").call(this,{body:a,headers:c})}}aw=c1,ax=new WeakMap,av=new WeakSet,ay=function(){return"https://api.openai.com/v1"!==this.baseURL},c1.OpenAI=aw,c1.DEFAULT_TIMEOUT=6e5,c1.OpenAIError=aI,c1.APIError=aJ,c1.APIConnectionError=aL,c1.APIConnectionTimeoutError=aM,c1.APIUserAbortError=aK,c1.NotFoundError=aQ,c1.ConflictError=aR,c1.RateLimitError=aT,c1.BadRequestError=aN,c1.AuthenticationError=aO,c1.InternalServerError=aU,c1.PermissionDeniedError=aP,c1.UnprocessableEntityError=aS,c1.InvalidWebhookSignatureError=aX,c1.toFile=bQ,c1.Completions=cv,c1.Chat=cc,c1.Embeddings=cz,c1.Files=cD,c1.Images=cO,c1.Audio=ci,c1.Moderations=cQ,c1.Models=cP,c1.FineTuning=cL,c1.Graders=cN,c1.VectorStores=c_,c1.Webhooks=c0,c1.Beta=cu,c1.Batches=cj,c1.Uploads=cX,c1.Responses=cV,c1.Evals=cC,c1.Containers=cy}};