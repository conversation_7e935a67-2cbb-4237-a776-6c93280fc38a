<!DOCTYPE html><!--WsWNPj7rY9b__38HFwNNB--><html lang="fr"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/ca5b0344af6fbe2c.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-6325d97008443c31.js"/><script src="/_next/static/chunks/4bd1b696-cf72ae8a39fa05aa.js" async=""></script><script src="/_next/static/chunks/964-d6e2a37b7965f281.js" async=""></script><script src="/_next/static/chunks/main-app-a4e9da4cb2627252.js" async=""></script><script src="/_next/static/chunks/598-0915b3b2be220ebc.js" async=""></script><script src="/_next/static/chunks/874-437a265a67d6cfee.js" async=""></script><script src="/_next/static/chunks/app/layout-36d5455f7f2eae6a.js" async=""></script><script src="/_next/static/chunks/110-00434082548dfaee.js" async=""></script><script src="/_next/static/chunks/app/error-ba519935ca0333ee.js" async=""></script><script src="/_next/static/chunks/app/not-found-db8394f455dee4e4.js" async=""></script><script src="/_next/static/chunks/app/test-simple/page-deaa14dd42f069cb.js" async=""></script><script src="/_next/static/chunks/app/loading-ffc478a7c2c2f15a.js" async=""></script><script src="/_next/static/chunks/app/global-error-a450fa68e65b8143.js" async=""></script><title>Leoni Agents - Système d&#x27;Agents Intelligents</title><meta name="description" content="Système d&#x27;agents pour l&#x27;analyse d&#x27;erreurs et la génération SQL"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_5cfdac __variable_9a8899 antialiased bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 min-h-screen"><div hidden=""><!--$--><!--/$--></div><div class="relative min-h-screen"><div class="absolute inset-0 opacity-30"><div class="absolute inset-0 bg-gradient-to-r from-blue-100/50 to-purple-100/50"></div><div class="absolute inset-0" style="background-image:radial-gradient(circle at 1px 1px, rgba(99, 102, 241, 0.1) 1px, transparent 0);background-size:20px 20px"></div></div><nav class="relative"><div class="bg-gradient-to-r from-[#002857] to-[#003d7a] shadow-xl shadow-[#002857]/20 border-b border-[#003d7a]/30"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex justify-between h-20 items-center"><div class="flex items-center space-x-3"><div class="relative"><div class="w-12 h-12 bg-gradient-to-r from-[#ff7514] to-[#ff8c42] rounded-xl flex items-center justify-center shadow-lg shadow-[#ff7514]/30"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sparkles w-7 h-7 text-white" aria-hidden="true"><path d="M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z"></path><path d="M20 2v4"></path><path d="M22 4h-4"></path><circle cx="4" cy="20" r="2"></circle></svg></div><div class="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-[#ff8c42] to-[#ff7514] rounded-full animate-pulse"></div></div><div><h1 class="text-2xl font-bold text-white">Leoni Agents</h1><p class="text-xs text-[#ff8c42] font-medium">Intelligence Artificielle</p></div></div><div class="hidden sm:flex sm:space-x-2"><a class="group relative inline-flex items-center px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 transform hover:scale-105 text-white/80 hover:text-white hover:bg-white/10 hover:shadow-md" href="/"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-house w-5 h-5 mr-2 transition-all duration-300 text-white/70 group-hover:text-[#ff8c42]" aria-hidden="true"><path d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8"></path><path d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path></svg>Accueil</a><a class="group relative inline-flex items-center px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 transform hover:scale-105 text-white/80 hover:text-white hover:bg-white/10 hover:shadow-md" href="/error-analysis"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bug w-5 h-5 mr-2 transition-all duration-300 text-white/70 group-hover:text-[#ff8c42]" aria-hidden="true"><path d="m8 2 1.88 1.88"></path><path d="M14.12 3.88 16 2"></path><path d="M9 7.13v-1a3.003 3.003 0 1 1 6 0v1"></path><path d="M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6"></path><path d="M12 20v-9"></path><path d="M6.53 9C4.6 8.8 3 7.1 3 5"></path><path d="M6 13H2"></path><path d="M3 21c0-2.1 1.7-3.9 3.8-4"></path><path d="M20.97 5c0 2.1-1.6 3.8-3.5 4"></path><path d="M22 13h-4"></path><path d="M17.2 17c2.1.1 3.8 1.9 3.8 4"></path></svg>Analyse d&#x27;Erreurs</a><a class="group relative inline-flex items-center px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 transform hover:scale-105 text-white/80 hover:text-white hover:bg-white/10 hover:shadow-md" href="/sql-generation"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database w-5 h-5 mr-2 transition-all duration-300 text-white/70 group-hover:text-[#ff8c42]" aria-hidden="true"><ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M3 5V19A9 3 0 0 0 21 19V5"></path><path d="M3 12A9 3 0 0 0 21 12"></path></svg>Génération SQL</a></div></div></div></div><div class="sm:hidden px-4 pt-2 pb-3 space-y-1"><a class="block pl-3 pr-4 py-2 border-l-4 text-base font-medium border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700" href="/"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-house w-4 h-4 mr-3" aria-hidden="true"><path d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8"></path><path d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path></svg>Accueil</div></a><a class="block pl-3 pr-4 py-2 border-l-4 text-base font-medium border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700" href="/error-analysis"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bug w-4 h-4 mr-3" aria-hidden="true"><path d="m8 2 1.88 1.88"></path><path d="M14.12 3.88 16 2"></path><path d="M9 7.13v-1a3.003 3.003 0 1 1 6 0v1"></path><path d="M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6"></path><path d="M12 20v-9"></path><path d="M6.53 9C4.6 8.8 3 7.1 3 5"></path><path d="M6 13H2"></path><path d="M3 21c0-2.1 1.7-3.9 3.8-4"></path><path d="M20.97 5c0 2.1-1.6 3.8-3.5 4"></path><path d="M22 13h-4"></path><path d="M17.2 17c2.1.1 3.8 1.9 3.8 4"></path></svg>Analyse d&#x27;Erreurs</div></a><a class="block pl-3 pr-4 py-2 border-l-4 text-base font-medium border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700" href="/sql-generation"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database w-4 h-4 mr-3" aria-hidden="true"><ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M3 5V19A9 3 0 0 0 21 19V5"></path><path d="M3 12A9 3 0 0 0 21 12"></path></svg>Génération SQL</div></a></div></nav><main class="relative z-10"><!--$--><div style="padding:20px"><h1>Test Ultra-Simple</h1><p>Si vous voyez cette page, React fonctionne.</p><button style="padding:10px 20px;background-color:green;color:white;border:none;border-radius:5px;cursor:pointer;font-size:16px">Cliquez-moi !</button><div style="margin-top:20px"><p>Si le bouton ne fonctionne pas, il y a un problème JavaScript global.</p></div></div><!--$--><!--/$--><!--/$--></main><div class="fixed top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl animate-pulse"></div><div class="fixed bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-indigo-400/20 to-pink-400/20 rounded-full blur-xl animate-pulse delay-1000"></div></div><script src="/_next/static/chunks/webpack-6325d97008443c31.js" id="_R_" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[993,[\"598\",\"static/chunks/598-0915b3b2be220ebc.js\",\"874\",\"static/chunks/874-437a265a67d6cfee.js\",\"177\",\"static/chunks/app/layout-36d5455f7f2eae6a.js\"],\"default\"]\n3:I[7555,[],\"\"]\n4:I[1901,[\"598\",\"static/chunks/598-0915b3b2be220ebc.js\",\"110\",\"static/chunks/110-00434082548dfaee.js\",\"39\",\"static/chunks/app/error-ba519935ca0333ee.js\"],\"default\"]\n5:I[1295,[],\"\"]\n6:I[1544,[\"598\",\"static/chunks/598-0915b3b2be220ebc.js\",\"874\",\"static/chunks/874-437a265a67d6cfee.js\",\"345\",\"static/chunks/app/not-found-db8394f455dee4e4.js\"],\"default\"]\n7:I[894,[],\"ClientPageRoot\"]\n8:I[2234,[\"352\",\"static/chunks/app/test-simple/page-deaa14dd42f069cb.js\"],\"default\"]\nb:I[9665,[],\"OutletBoundary\"]\nd:I[4911,[],\"AsyncMetadataOutlet\"]\nf:I[8460,[\"209\",\"static/chunks/app/loading-ffc478a7c2c2f15a.js\"],\"default\"]\n10:I[9665,[],\"ViewportBoundary\"]\n12:I[9665,[],\"MetadataBoundary\"]\n13:\"$Sreact.suspense\"\n15:I[8385,[\"219\",\"static/chunks/app/global-error-a450fa68e65b8143.js\"],\"default\"]\n:HL[\"/_next/static/css/ca5b0344af6fbe2c.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"WsWNPj7rY9b__38HFwNNB\",\"p\":\"\",\"c\":[\"\",\"test-simple\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"test-simple\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/ca5b0344af6fbe2c.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"fr\",\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_5cfdac __variable_9a8899 antialiased bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 min-h-screen\",\"children\":[\"$\",\"div\",null,{\"className\":\"relative min-h-screen\",\"children\":[[\"$\",\"div\",null,{\"className\":\"absolute inset-0 opacity-30\",\"children\":[[\"$\",\"div\",null,{\"className\":\"absolute inset-0 bg-gradient-to-r from-blue-100/50 to-purple-100/50\"}],[\"$\",\"div\",null,{\"className\":\"absolute inset-0\",\"style\":{\"backgroundImage\":\"radial-gradient(circle at 1px 1px, rgba(99, 102, 241, 0.1) 1px, transparent 0)\",\"backgroundSize\":\"20px 20px\"}}]]}],[\"$\",\"$L2\",null,{}],[\"$\",\"main\",null,{\"className\":\"relative z-10\",\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$4\",\"errorStyles\":[],\"errorScripts\":[],\"template\":[\"$\",\"$L5\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"$L6\",null,{}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"div\",null,{\"className\":\"fixed top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-xl animate-pulse\"}],[\"$\",\"div\",null,{\"className\":\"fixed bottom-20 right-10 w-40 h-40 bg-gradient-to-r from-indigo-400/20 to-pink-400/20 rounded-full blur-xl animate-pulse delay-1000\"}]]}]}]}]]}],{\"children\":[\"test-simple\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L5\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L7\",null,{\"Component\":\"$8\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@9\",\"$@a\"]}],null,[\"$\",\"$Lb\",null,{\"children\":[\"$Lc\",[\"$\",\"$Ld\",null,{\"promise\":\"$@e\"}]]}]]}],{},null,false]},null,false]},[[\"$\",\"$Lf\",\"l\",{}],[],[]],false],[\"$\",\"$1\",\"h\",{\"children\":[null,[[\"$\",\"$L10\",null,{\"children\":\"$L11\"}],null],[\"$\",\"$L12\",null,{\"children\":[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$13\",null,{\"fallback\":null,\"children\":\"$L14\"}]}]}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$15\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"9:{}\na:\"$0:f:0:1:2:children:2:children:1:props:children:0:props:params\"\n"])</script><script>self.__next_f.push([1,"11:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nc:null\n"])</script><script>self.__next_f.push([1,"16:I[8175,[],\"IconMark\"]\ne:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Leoni Agents - Système d'Agents Intelligents\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Système d'agents pour l'analyse d'erreurs et la génération SQL\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}],[\"$\",\"$L16\",\"3\",{}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"14:\"$e:metadata\"\n"])</script></body></html>