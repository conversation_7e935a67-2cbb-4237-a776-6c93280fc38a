(()=>{var a={};a.id=164,a.ids=[164],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4947:(a,b,c)=>{Promise.resolve().then(c.bind(c,60705))},7959:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>o});var d=c(60687),e=c(43210),f=c(44493),g=c(29523),h=c(34729),i=c(89667),j=c(10022),k=c(93613),l=c(48730),m=c(48210);let n=(0,c(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);function o(){let[a,b]=(0,e.useState)({name:"",content:"",type:"program"}),[c,o]=(0,e.useState)({name:"",content:"",type:"error"}),[p,q]=(0,e.useState)(!1),[r,s]=(0,e.useState)(null),[t,u]=(0,e.useState)(""),v=(a,c)=>{let d=a.target.files?.[0];if(d){let a=new FileReader;a.onload=a=>{let e=a.target?.result;"program"===c?b({name:d.name,content:e,type:"program"}):o({name:d.name,content:e,type:"error"})},a.readAsText(d)}},w=async()=>{if(!a.content)return void u("Veuillez fournir au moins le fichier programme");q(!0),u(""),s(null);try{let b=c.content?"/api/error-analysis":"/api/error-analysis/static",d=c.content?{programFile:a,errorFile:c}:{programFile:a},e=await fetch(b,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(d)}),f=await e.json();if(!e.ok)throw Error(f.error||"Erreur lors de l'analyse");s(f.data)}catch(a){u(a instanceof Error?a.message:"Une erreur est survenue lors de l'analyse")}finally{q(!1)}};return(0,d.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,d.jsxs)("div",{className:"text-center mb-8",children:[(0,d.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Analyse d'Erreurs"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Analysez vos fichiers de programme et d'erreur pour obtenir des diagnostics d\xe9taill\xe9s."})]}),(0,d.jsxs)("div",{className:"grid lg:grid-cols-2 gap-8",children:[(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsxs)(f.ZB,{className:"flex items-center",children:[(0,d.jsx)(j.A,{className:"w-5 h-5 mr-2"}),"Fichier Programme"]}),(0,d.jsx)(f.BT,{children:"T\xe9l\xe9chargez ou collez le contenu de votre fichier programme"})]}),(0,d.jsxs)(f.Wu,{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(i.p,{type:"file",accept:".txt,.c,.cpp,.ec,.log",onChange:a=>v(a,"program"),className:"mb-2"}),a.name&&(0,d.jsxs)("p",{className:"text-sm text-green-600",children:["✓ ",a.name]})]}),(0,d.jsx)(h.T,{placeholder:"Ou collez le contenu du fichier programme ici...",value:a.content,onChange:c=>b({...a,content:c.target.value}),className:"min-h-[200px] font-mono text-sm"})]})]}),(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{children:[(0,d.jsxs)(f.ZB,{className:"flex items-center",children:[(0,d.jsx)(k.A,{className:"w-5 h-5 mr-2"}),"Fichier d'Erreur"]}),(0,d.jsx)(f.BT,{children:"T\xe9l\xe9chargez ou collez le contenu de votre fichier d'erreur (optionnel - l'analyse statique sera effectu\xe9e si aucun fichier d'erreur n'est fourni)"})]}),(0,d.jsxs)(f.Wu,{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(i.p,{type:"file",accept:".txt,.error,.log",onChange:a=>v(a,"error"),className:"mb-2"}),c.name&&(0,d.jsxs)("p",{className:"text-sm text-green-600",children:["✓ ",c.name]})]}),(0,d.jsx)(h.T,{placeholder:"Ou collez le contenu du fichier d'erreur ici... (Laissez vide pour une analyse statique du code uniquement)",value:c.content,onChange:a=>o({...c,content:a.target.value}),className:"min-h-[200px] font-mono text-sm"})]})]}),(0,d.jsx)(g.$,{onClick:w,disabled:p||!a.content||!c.content,className:"w-full",size:"lg",children:p?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(l.A,{className:"w-5 h-5 mr-2 animate-spin"}),"Analyse en cours..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(m.A,{className:"w-5 h-5 mr-2"}),c.content?"Analyser les Erreurs":"Analyse Statique du Code"]})}),(0,d.jsx)("div",{className:"text-center text-sm text-gray-600 mt-2",children:c.content?(0,d.jsxs)("span",{className:"flex items-center justify-center",children:[(0,d.jsx)(k.A,{className:"w-4 h-4 mr-1"}),"Analyse compl\xe8te avec fichier d'erreur"]}):(0,d.jsxs)("span",{className:"flex items-center justify-center",children:[(0,d.jsx)(j.A,{className:"w-4 h-4 mr-1"}),"Analyse statique du code uniquement"]})}),t&&(0,d.jsx)("div",{className:"bg-[#ff7514]/5 border border-[#ff7514]/20 rounded-lg p-4",children:(0,d.jsx)("p",{className:"text-[#ff7514]",children:t})})]}),(0,d.jsxs)("div",{children:[r&&(0,d.jsxs)(f.Zp,{className:"mt-8",children:[(0,d.jsxs)(f.aR,{className:"bg-[#002857] text-white",children:[(0,d.jsxs)(f.ZB,{className:"flex items-center",children:[(0,d.jsx)(n,{className:"w-5 h-5 mr-2"}),"R\xe9sultats de l'Analyse"]}),(0,d.jsxs)(f.BT,{className:"text-blue-100",children:["Analyse termin\xe9e le ",new Date(r.timestamp).toLocaleString("fr-FR")]})]}),(0,d.jsxs)(f.Wu,{className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold mb-2",children:"R\xe9sum\xe9"}),(0,d.jsx)("p",{className:"text-gray-700",children:r.summary})]}),r.errors.length>0&&(0,d.jsxs)("div",{children:[(0,d.jsxs)("h3",{className:"font-semibold mb-3",children:["Erreurs D\xe9tect\xe9es (",r.errors.length,")"]}),(0,d.jsx)("div",{className:"space-y-4",children:r.errors.map((a,b)=>(0,d.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,d.jsx)("h4",{className:"font-medium",children:a.errorType}),(0,d.jsx)("span",{className:`px-2 py-1 rounded text-xs font-medium ${(a=>{switch(a){case"CRITICAL":return"text-red-600 bg-red-50";case"HIGH":return"text-orange-600 bg-orange-50";case"MEDIUM":return"text-yellow-600 bg-yellow-50";case"LOW":return"text-green-600 bg-green-50";default:return"text-gray-600 bg-gray-50"}})(a.severity)}`,children:a.severity})]}),(0,d.jsxs)("p",{className:"text-sm text-gray-600 mb-2",children:[(0,d.jsx)("strong",{children:"Emplacement:"})," ",a.location,a.lineNumber&&(0,d.jsxs)("span",{className:"ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs",children:["Ligne ",a.lineNumber]}),a.fileName&&(0,d.jsx)("span",{className:"ml-2 px-2 py-1 bg-green-100 text-green-800 rounded text-xs",children:a.fileName})]}),(0,d.jsx)("p",{className:"text-sm mb-3",children:a.description}),a.codeContext&&(0,d.jsxs)("div",{className:"mb-3",children:[(0,d.jsx)("p",{className:"text-sm font-medium mb-2",children:"Contexte du code:"}),(0,d.jsx)("div",{className:"bg-gray-900 text-gray-100 p-3 rounded text-xs font-mono overflow-x-auto",children:a.codeContext.contextLines.map(a=>(0,d.jsxs)("div",{className:`flex ${a.isTarget?"bg-red-900/50":""}`,children:[(0,d.jsx)("span",{className:"text-gray-400 mr-3 w-8 text-right",children:a.number}),(0,d.jsx)("span",{className:a.isTarget?"text-red-300":"",children:a.content||" "})]},a.number))})]}),a.possibleCauses.length>0&&(0,d.jsxs)("div",{className:"mb-3",children:[(0,d.jsx)("p",{className:"text-sm font-medium mb-1",children:"Causes possibles:"}),(0,d.jsx)("ul",{className:"text-sm text-gray-600 list-disc list-inside",children:a.possibleCauses.map((a,b)=>(0,d.jsx)("li",{children:a},b))})]}),a.solutions.length>0&&(0,d.jsxs)("div",{className:"mb-3",children:[(0,d.jsx)("p",{className:"text-sm font-medium mb-1",children:"Solutions recommand\xe9es:"}),(0,d.jsx)("ul",{className:"text-sm text-gray-600 list-disc list-inside",children:a.solutions.map((a,b)=>(0,d.jsx)("li",{children:a},b))})]}),a.autoFixSuggestions&&a.autoFixSuggestions.length>0&&(0,d.jsxs)("div",{className:"mb-3",children:[(0,d.jsx)("p",{className:"text-sm font-medium mb-2",children:"Corrections automatiques sugg\xe9r\xe9es:"}),(0,d.jsx)("div",{className:"space-y-2",children:a.autoFixSuggestions.map((a,b)=>(0,d.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded p-3",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,d.jsx)("span",{className:"text-sm font-medium text-green-800",children:a.type}),(0,d.jsx)("span",{className:`px-2 py-1 rounded text-xs ${"HIGH"===a.confidence?"bg-green-100 text-green-800":"MEDIUM"===a.confidence?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:a.confidence})]}),(0,d.jsx)("p",{className:"text-sm text-green-700 mb-2",children:a.description}),a.suggestedCode&&(0,d.jsx)("div",{className:"bg-green-900 text-green-100 p-2 rounded text-xs font-mono",children:a.suggestedCode})]},b))})]}),a.relatedErrors&&a.relatedErrors.length>0&&(0,d.jsxs)("div",{className:"mb-3",children:[(0,d.jsx)("p",{className:"text-sm font-medium mb-1",children:"Erreurs li\xe9es:"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-1",children:a.relatedErrors.map(a=>(0,d.jsxs)("span",{className:"px-2 py-1 bg-orange-100 text-orange-800 rounded text-xs",children:["Erreur #",a+1]},a))})]}),a.codeContext?.analysis&&(0,d.jsxs)("div",{className:"mb-3",children:[(0,d.jsx)("p",{className:"text-sm font-medium mb-2",children:"Analyse contextuelle:"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 text-xs",children:[a.codeContext.analysis.variables.length>0&&(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium mb-1",children:"Variables d\xe9tect\xe9es:"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-1",children:a.codeContext.analysis.variables.map((a,b)=>(0,d.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 rounded",children:a},b))})]}),a.codeContext.analysis.functions.length>0&&(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium mb-1",children:"Fonctions d\xe9tect\xe9es:"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-1",children:a.codeContext.analysis.functions.map((a,b)=>(0,d.jsxs)("span",{className:"px-2 py-1 bg-purple-100 text-purple-800 rounded",children:[a,"()"]},b))})]})]}),a.codeContext.analysis.potentialIssues.length>0&&(0,d.jsxs)("div",{className:"mt-2",children:[(0,d.jsx)("p",{className:"font-medium mb-1",children:"Probl\xe8mes potentiels:"}),(0,d.jsx)("ul",{className:"text-xs text-gray-600 list-disc list-inside",children:a.codeContext.analysis.potentialIssues.map((a,b)=>(0,d.jsx)("li",{children:a},b))})]})]})]},b))})]}),r.recommendations.length>0&&(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold mb-2",children:"Recommandations G\xe9n\xe9rales"}),(0,d.jsx)("ul",{className:"space-y-1",children:r.recommendations.map((a,b)=>(0,d.jsxs)("li",{className:"text-sm text-gray-700 flex items-start",children:[(0,d.jsx)("span",{className:"text-blue-500 mr-2",children:"•"}),a]},b))})]})]})]}),!r&&!p&&(0,d.jsx)(f.Zp,{className:"bg-white/60 backdrop-blur-xl border-0 shadow-xl rounded-3xl",children:(0,d.jsxs)(f.Wu,{className:"text-center py-16",children:[(0,d.jsxs)("div",{className:"relative mb-6",children:[(0,d.jsx)("div",{className:"w-24 h-24 mx-auto bg-gradient-to-r from-gray-200 to-gray-300 rounded-3xl flex items-center justify-center",children:(0,d.jsx)(m.A,{className:"w-12 h-12 text-gray-400"})}),(0,d.jsx)("div",{className:"absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-pulse"})]}),(0,d.jsx)("h3",{className:"text-xl font-bold text-gray-700 mb-3",children:"Pr\xeat pour l'Analyse"}),(0,d.jsx)("p",{className:"text-gray-500 max-w-md mx-auto leading-relaxed",children:"Les r\xe9sultats de l'analyse appara\xeetront ici une fois que vous aurez t\xe9l\xe9charg\xe9 vos fichiers et lanc\xe9 l'analyse."}),(0,d.jsxs)("div",{className:"mt-6 flex justify-center space-x-4 text-sm text-gray-400",children:[(0,d.jsx)("span",{children:"\uD83D\uDD0D D\xe9tection automatique"}),(0,d.jsx)("span",{children:"\uD83D\uDCCD Localisation pr\xe9cise"}),(0,d.jsx)("span",{children:"\uD83D\uDCA1 Solutions expertes"})]})]})})]})]})]})}},10022:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17774:(a,b,c)=>{Promise.resolve().then(c.bind(c,7959))},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34729:(a,b,c)=>{"use strict";c.d(b,{T:()=>g});var d=c(60687),e=c(43210),f=c(4780);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("textarea",{className:(0,f.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:c,...b}));g.displayName="Textarea"},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},44493:(a,b,c)=>{"use strict";c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h});var d=c(60687),e=c(43210),f=c(4780);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("h3",{ref:c,className:(0,f.cn)("text-2xl font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("p",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b})).displayName="CardFooter"},48730:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},51527:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>B.default,__next_app__:()=>H,handler:()=>J,pages:()=>G,routeModule:()=>I,tree:()=>F});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(31369),C=c(30893),D=c(52836),E={};for(let a in C)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(E[a]=()=>C[a]);c.d(b,E);let F={children:["",{children:["error-analysis",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,76080)),"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\error-analysis\\page.tsx"]}]},{error:[()=>Promise.resolve().then(c.bind(c,89539)),"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\error-analysis\\error.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(c.bind(c,54431)),"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\error.tsx"],loading:[()=>Promise.resolve().then(c.bind(c,67393)),"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\loading.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,31369)),"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.bind(c,31369)),"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\global-error.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,G=["C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\error-analysis\\page.tsx"],H={require:c,loadChunk:()=>Promise.resolve()},I=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/error-analysis/page",pathname:"/error-analysis",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:F},distDir:".next",projectDir:""});async function J(a,b,c){var d;let E="/error-analysis/page";"/index"===E&&(E="/");let K="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await I.prepare(a,b,{srcPage:E,multiZoneDraftMode:K});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(E),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===I.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&I.isDev&&(az=_);let aA={...C,tree:F,pages:G,GlobalError:B.default,handler:J,routeModule:I,__next_app__:H};W&&X&&(0,n.setReferenceManifestsSingleton)({page:E,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return I.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:I,page:E,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:I.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:K,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>I.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:I.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!I.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===I.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await I.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await I.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),I.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!I.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&D.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await I.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},51803:(a,b,c)=>{Promise.resolve().then(c.bind(c,89539))},57094:(a,b,c)=>{Promise.resolve().then(c.bind(c,76080))},60705:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>k});var d=c(60687);c(43210);var e=c(29523),f=c(93613),g=c(78122),h=c(32192),i=c(85814),j=c.n(i);function k({error:a,reset:b}){return(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 p-4",children:(0,d.jsx)("div",{className:"max-w-md w-full",children:(0,d.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8 text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r from-[#ff7514] to-[#ff8c42] rounded-full flex items-center justify-center mx-auto mb-6",children:(0,d.jsx)(f.A,{className:"w-8 h-8 text-white"})}),(0,d.jsx)("h1",{className:"text-2xl font-bold text-[#002857] mb-4",children:"Erreur d'Analyse"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:"Une erreur s'est produite lors de l'analyse des erreurs. Veuillez r\xe9essayer ou retourner \xe0 l'accueil."}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)(e.$,{onClick:b,className:"w-full bg-gradient-to-r from-[#ff7514] to-[#ff8c42] hover:from-[#e6650f] hover:to-[#ff7514] text-white",children:[(0,d.jsx)(g.A,{className:"w-4 h-4 mr-2"}),"R\xe9essayer l'analyse"]}),(0,d.jsx)(j(),{href:"/",children:(0,d.jsxs)(e.$,{variant:"outline",className:"w-full border-[#002857] text-[#002857] hover:bg-[#002857] hover:text-white",children:[(0,d.jsx)(h.A,{className:"w-4 h-4 mr-2"}),"Retour \xe0 l'accueil"]})})]})]})})})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},76080:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Leoni\\\\leoni-agents\\\\src\\\\app\\\\error-analysis\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\error-analysis\\page.tsx","default")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},89539:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Leoni\\\\leoni-agents\\\\src\\\\app\\\\error-analysis\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\Leoni\\leoni-agents\\src\\app\\error-analysis\\error.tsx","default")},89667:(a,b,c)=>{"use strict";c.d(b,{p:()=>g});var d=c(60687),e=c(43210),f=c(4780);let g=e.forwardRef(({className:a,type:b,...c},e)=>(0,d.jsx)("input",{type:b,className:(0,f.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:e,...c}));g.displayName="Input"}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,565,748],()=>b(b.s=51527));module.exports=c})();